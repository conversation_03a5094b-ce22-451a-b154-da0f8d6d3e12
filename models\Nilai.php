<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/Absensi.php';
require_once __DIR__ . '/Tugas.php';
require_once __DIR__ . '/NilaiPengganti.php';

class Nilai {
    private $conn;
    private $table_name = "nilai";

    public $id;
    public $siswa_id;
    public $mapel_id;
    public $nilai_tugas;
    public $nilai_uts;
    public $nilai_uas;
    public $nilai_absen;
    public $nilai_akhir;
    public $rumus_nilai;
    public $semester;
    public $tahun_ajaran;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function hitungNilaiAbsensi($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $absensi = new Absensi();

        // Convert semester and tahun_ajaran to date range
        $tahun = explode('/', $tahun_ajaran)[0];
        $start_date = $semester == '1' ?
            $tahun . '-07-01' :  // First semester starts in July
            ($tahun + 1) . '-01-01';  // Second semester starts in January

        $end_date = $semester == '1' ?
            $tahun . '-12-31' :  // First semester ends in December
            ($tahun + 1) . '-06-30';  // Second semester ends in June

        // Get attendance records for the date range
        $query = "SELECT
                    COUNT(CASE WHEN d.status = 'Hadir' THEN 1 END) as hadir,
                    COUNT(CASE WHEN d.status = 'Sakit' THEN 1 END) as sakit,
                    COUNT(CASE WHEN d.status = 'Izin' THEN 1 END) as izin,
                    COUNT(CASE WHEN d.status = 'Alpha' THEN 1 END) as alpha,
                    COUNT(*) as total_pertemuan
                FROM detail_absensi d
                JOIN absensi a ON d.absensi_id = a.id
                WHERE d.siswa_id = :siswa_id
                AND a.mapel_id = :mapel_id
                AND a.tanggal BETWEEN :start_date AND :end_date";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":start_date", $start_date);
        $stmt->bindParam(":end_date", $end_date);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        // Calculate attendance score (100 point scale)
        // Hadir: 100%, Sakit: 75%, Izin: 50%, Alpha: 0%
        if($row['total_pertemuan'] > 0) {
            $score = (
                ($row['hadir'] * 100) +
                ($row['sakit'] * 75) +
                ($row['izin'] * 50) +
                ($row['alpha'] * 0)
            ) / $row['total_pertemuan'];

            return round($score, 2);
        }

        return 100; // Default if no attendance records
    }

    /**
     * Get the actual grade values considering replacements
     *
     * @return array Associative array with actual grade values
     */
    public function getNilaiAktual() {
        $nilai_tugas = $this->nilai_tugas;
        $nilai_uts = $this->nilai_uts;
        $nilai_uas = $this->nilai_uas;
        $nilai_absen = $this->nilai_absen;

        // Check for replacements
        if ($this->id) {
            $nilaiPengganti = new NilaiPengganti();

            // Check for tugas replacement
            $tugas_replacement = $nilaiPengganti->getByNilaiIdAndJenis($this->id, 'nilai_tugas');
            if ($tugas_replacement && $tugas_replacement->rowCount() > 0) {
                $replacement = $tugas_replacement->fetch(PDO::FETCH_ASSOC);

                // Check if it's an average replacement
                if (isset($replacement['is_average']) && $replacement['is_average'] == 1) {
                    // Calculate average from all tugas tambahan
                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                        $this->siswa_id,
                        $this->mapel_id,
                        $this->semester,
                        $this->tahun_ajaran
                    );
                    $nilai_tugas = $avg_result['rata_nilai'];
                } else {
                    $nilai_tugas = $replacement['nilai_pengganti'];
                }
            }

            // Check for UTS replacement
            $uts_replacement = $nilaiPengganti->getByNilaiIdAndJenis($this->id, 'nilai_uts');
            if ($uts_replacement && $uts_replacement->rowCount() > 0) {
                $replacement = $uts_replacement->fetch(PDO::FETCH_ASSOC);

                // Check if it's an average replacement
                if (isset($replacement['is_average']) && $replacement['is_average'] == 1) {
                    // Calculate average from all tugas tambahan
                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                        $this->siswa_id,
                        $this->mapel_id,
                        $this->semester,
                        $this->tahun_ajaran
                    );
                    $nilai_uts = $avg_result['rata_nilai'];
                } else {
                    $nilai_uts = $replacement['nilai_pengganti'];
                }
            }

            // Check for UAS replacement
            $uas_replacement = $nilaiPengganti->getByNilaiIdAndJenis($this->id, 'nilai_uas');
            if ($uas_replacement && $uas_replacement->rowCount() > 0) {
                $replacement = $uas_replacement->fetch(PDO::FETCH_ASSOC);

                // Check if it's an average replacement
                if (isset($replacement['is_average']) && $replacement['is_average'] == 1) {
                    // Calculate average from all tugas tambahan
                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                        $this->siswa_id,
                        $this->mapel_id,
                        $this->semester,
                        $this->tahun_ajaran
                    );
                    $nilai_uas = $avg_result['rata_nilai'];
                } else {
                    $nilai_uas = $replacement['nilai_pengganti'];
                }
            }

            // Check for Absen replacement
            $absen_replacement = $nilaiPengganti->getByNilaiIdAndJenis($this->id, 'nilai_absen');
            if ($absen_replacement && $absen_replacement->rowCount() > 0) {
                $replacement = $absen_replacement->fetch(PDO::FETCH_ASSOC);

                // Check if it's an average replacement
                if (isset($replacement['is_average']) && $replacement['is_average'] == 1) {
                    // Calculate average from all tugas tambahan
                    $avg_result = $nilaiPengganti->calculateAverageNilai(
                        $this->siswa_id,
                        $this->mapel_id,
                        $this->semester,
                        $this->tahun_ajaran
                    );
                    $nilai_absen = $avg_result['rata_nilai'];
                } else {
                    $nilai_absen = $replacement['nilai_pengganti'];
                }
            }
        }

        return [
            'nilai_tugas' => $nilai_tugas,
            'nilai_uts' => $nilai_uts,
            'nilai_uas' => $nilai_uas,
            'nilai_absen' => $nilai_absen
        ];
    }

    public function hitungNilaiAkhir() {
        // Get actual values considering replacements
        $nilai_aktual = $this->getNilaiAktual();

        if(empty($this->rumus_nilai)) {
            // Default formula if none provided
            return ($nilai_aktual['nilai_tugas'] * 0.3) +
                   ($nilai_aktual['nilai_uts'] * 0.3) +
                   ($nilai_aktual['nilai_uas'] * 0.3) +
                   ($nilai_aktual['nilai_absen'] * 0.1);
        }

        // Parse and evaluate the custom formula
        $formula = $this->rumus_nilai;
        $formula = str_replace(
            ['[TUGAS]', '[UTS]', '[UAS]', '[ABSEN]'],
            [
                $nilai_aktual['nilai_tugas'],
                $nilai_aktual['nilai_uts'],
                $nilai_aktual['nilai_uas'],
                $nilai_aktual['nilai_absen']
            ],
            $formula
        );

        // Evaluate the formula safely
        $formula = preg_replace('/[^0-9\+\-\*\/\(\)\.\s]/', '', $formula);

        try {
            return eval('return ' . $formula . ';');
        } catch(Exception $e) {
            // If there's an error in the formula, use default
            return ($nilai_aktual['nilai_tugas'] * 0.3) +
                   ($nilai_aktual['nilai_uts'] * 0.3) +
                   ($nilai_aktual['nilai_uas'] * 0.3) +
                   ($nilai_aktual['nilai_absen'] * 0.1);
        }
    }

    public function create() {
        // Get average assignment score
        $tugas = new Tugas();
        $nilai_tugas = $tugas->getRataRataNilaiTugas(
            $this->siswa_id,
            $this->mapel_id,
            $this->semester,
            $this->tahun_ajaran
        );

        // Get attendance score
        $nilai_absen = $this->hitungNilaiAbsensi(
            $this->siswa_id,
            $this->mapel_id,
            $this->semester,
            $this->tahun_ajaran
        );

        $query = "INSERT INTO " . $this->table_name . "
                (siswa_id, mapel_id, nilai_tugas, nilai_uts, nilai_uas, nilai_absen, nilai_akhir, rumus_nilai, semester, tahun_ajaran)
                VALUES
                (:siswa_id, :mapel_id, :nilai_tugas, :nilai_uts, :nilai_uas, :nilai_absen, :nilai_akhir, :rumus_nilai, :semester, :tahun_ajaran)
                ON DUPLICATE KEY UPDATE
                nilai_tugas = VALUES(nilai_tugas),
                nilai_uts = VALUES(nilai_uts),
                nilai_uas = VALUES(nilai_uas),
                nilai_absen = VALUES(nilai_absen),
                nilai_akhir = VALUES(nilai_akhir),
                rumus_nilai = VALUES(rumus_nilai)";

        $stmt = $this->conn->prepare($query);

        // Calculate final grade
        $this->nilai_tugas = $nilai_tugas;
        $this->nilai_absen = $nilai_absen;
        $this->nilai_akhir = $this->hitungNilaiAkhir();

        // Bind values
        $stmt->bindParam(":siswa_id", $this->siswa_id);
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":nilai_tugas", $this->nilai_tugas);
        $stmt->bindParam(":nilai_uts", $this->nilai_uts);
        $stmt->bindParam(":nilai_uas", $this->nilai_uas);
        $stmt->bindParam(":nilai_absen", $this->nilai_absen);
        $stmt->bindParam(":nilai_akhir", $this->nilai_akhir);
        $stmt->bindParam(":rumus_nilai", $this->rumus_nilai);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);

        return $stmt->execute();
    }

    public function update() {
        // Get average assignment score
        $tugas = new Tugas();
        $nilai_tugas = $tugas->getRataRataNilaiTugas(
            $this->siswa_id,
            $this->mapel_id,
            $this->semester,
            $this->tahun_ajaran
        );

        // Get attendance score
        $nilai_absen = $this->hitungNilaiAbsensi(
            $this->siswa_id,
            $this->mapel_id,
            $this->semester,
            $this->tahun_ajaran
        );

        $query = "UPDATE " . $this->table_name . "
                SET nilai_tugas = :nilai_tugas,
                    nilai_uts = :nilai_uts,
                    nilai_uas = :nilai_uas,
                    nilai_absen = :nilai_absen,
                    nilai_akhir = :nilai_akhir,
                    rumus_nilai = :rumus_nilai
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Calculate final grade
        $this->nilai_tugas = $nilai_tugas;
        $this->nilai_absen = $nilai_absen;
        $this->nilai_akhir = $this->hitungNilaiAkhir();

        // Bind values
        $stmt->bindParam(":nilai_tugas", $this->nilai_tugas);
        $stmt->bindParam(":nilai_uts", $this->nilai_uts);
        $stmt->bindParam(":nilai_uas", $this->nilai_uas);
        $stmt->bindParam(":nilai_absen", $this->nilai_absen);
        $stmt->bindParam(":nilai_akhir", $this->nilai_akhir);
        $stmt->bindParam(":rumus_nilai", $this->rumus_nilai);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function getNilaiSiswa($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT n.*
                FROM " . $this->table_name . " n
                WHERE n.siswa_id = :siswa_id
                AND n.mapel_id = :mapel_id
                AND n.semester = :semester
                AND n.tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getNilaiMapel($mapel_id, $semester, $tahun_ajaran, $kelas_id = null) {
        // First get the tugas averages for all students
        $query = "SELECT n.*, s.nis, s.nama_siswa, k.nama_kelas,
                (SELECT COALESCE(AVG(nt.nilai), 0)
                FROM tugas t
                JOIN nilai_tugas nt ON t.id = nt.tugas_id
                WHERE t.mapel_id = n.mapel_id
                AND t.semester = n.semester
                AND t.tahun_ajaran = n.tahun_ajaran
                AND nt.siswa_id = n.siswa_id) as rata_tugas,

                -- Check for tugas replacement
                (SELECT tts.nilai
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_tugas'
                LIMIT 1) as nilai_tugas_pengganti,

                -- Get tugas replacement title
                (SELECT tt.judul
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_tugas'
                LIMIT 1) as judul_tugas_pengganti,

                -- Check if tugas uses average
                (SELECT np.is_average
                FROM nilai_pengganti np
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_tugas'
                LIMIT 1) as is_tugas_average,

                -- Get average tugas value
                (SELECT ROUND(AVG(tts.nilai), 2)
                FROM tugas_tambahan_siswa tts
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE tts.siswa_id = n.siswa_id
                AND tt.mapel_id = n.mapel_id
                AND tt.semester = n.semester
                AND tt.tahun_ajaran = n.tahun_ajaran
                AND tts.status = 'sudah_dikerjakan'
                AND tts.nilai IS NOT NULL) as nilai_tugas_rata_rata,

                -- Get count of tugas tambahan
                (SELECT COUNT(tts.id)
                FROM tugas_tambahan_siswa tts
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE tts.siswa_id = n.siswa_id
                AND tt.mapel_id = n.mapel_id
                AND tt.semester = n.semester
                AND tt.tahun_ajaran = n.tahun_ajaran
                AND tts.status = 'sudah_dikerjakan'
                AND tts.nilai IS NOT NULL) as jumlah_tugas_tambahan,

                -- Check for UTS replacement
                (SELECT tts.nilai
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_uts'
                LIMIT 1) as nilai_uts_pengganti,

                -- Get UTS replacement title
                (SELECT tt.judul
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_uts'
                LIMIT 1) as judul_uts_pengganti,

                -- Check if UTS uses average
                (SELECT np.is_average
                FROM nilai_pengganti np
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_uts'
                LIMIT 1) as is_uts_average,

                -- Check for UAS replacement
                (SELECT tts.nilai
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_uas'
                LIMIT 1) as nilai_uas_pengganti,

                -- Get UAS replacement title
                (SELECT tt.judul
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_uas'
                LIMIT 1) as judul_uas_pengganti,

                -- Check if UAS uses average
                (SELECT np.is_average
                FROM nilai_pengganti np
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_uas'
                LIMIT 1) as is_uas_average,

                -- Check for Absen replacement
                (SELECT tts.nilai
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_absen'
                LIMIT 1) as nilai_absen_pengganti,

                -- Get Absen replacement title
                (SELECT tt.judul
                FROM nilai_pengganti np
                JOIN tugas_tambahan_siswa tts ON np.tugas_tambahan_siswa_id = tts.id
                JOIN tugas_tambahan tt ON tts.tugas_tambahan_id = tt.id
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_absen'
                LIMIT 1) as judul_absen_pengganti,

                -- Check if Absen uses average
                (SELECT np.is_average
                FROM nilai_pengganti np
                WHERE np.nilai_id = n.id AND np.jenis_nilai = 'nilai_absen'
                LIMIT 1) as is_absen_average

                FROM " . $this->table_name . " n
                JOIN siswa s ON n.siswa_id = s.id
                JOIN kelas k ON s.kelas_id = k.id
                WHERE n.mapel_id = :mapel_id
                AND n.semester = :semester
                AND n.tahun_ajaran = :tahun_ajaran";

        if($kelas_id) {
            $query .= " AND s.kelas_id = :kelas_id";
        }

        $query .= " ORDER BY k.nama_kelas ASC, s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);

        if($kelas_id) {
            $stmt->bindParam(":kelas_id", $kelas_id);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getOne() {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->siswa_id = $row['siswa_id'];
            $this->mapel_id = $row['mapel_id'];
            $this->nilai_tugas = $row['nilai_tugas'];
            $this->nilai_uts = $row['nilai_uts'];
            $this->nilai_uas = $row['nilai_uas'];
            $this->nilai_absen = $row['nilai_absen'];
            $this->nilai_akhir = $row['nilai_akhir'];
            $this->rumus_nilai = $row['rumus_nilai'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            return true;
        }
        return false;
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        return $stmt->execute();
    }

    public function getSiswaByKelas($kelas_id) {
        $query = "SELECT s.id, s.nis, s.nama_siswa
                FROM siswa s
                WHERE s.kelas_id = :kelas_id AND s.status = 'aktif'
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->execute();

        return $stmt;
    }

    public function getAllSiswaByKelas($kelas_id, $include_alumni = false) {
        $query = "SELECT s.id, s.nis, s.nama_siswa, s.status
                FROM siswa s
                WHERE s.kelas_id = :kelas_id";

        if (!$include_alumni) {
            $query .= " AND s.status = 'aktif'";
        }

        $query .= " ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->execute();

        return $stmt;
    }

    public function getNilaiSiswaByNIS($nis, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT n.*
                FROM " . $this->table_name . " n
                JOIN siswa s ON n.siswa_id = s.id
                WHERE s.nis = :nis
                AND n.mapel_id = :mapel_id
                AND n.semester = :semester
                AND n.tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':nis', $nis);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getHistoricalGradesBySiswaId($siswa_id) {
        $query = "SELECT n.*, mp.nama_mapel, mp.kkm, k.nama_kelas, rk.tahun_ajaran as kelas_tahun
                FROM " . $this->table_name . " n
                JOIN mata_pelajaran mp ON n.mapel_id = mp.id
                LEFT JOIN riwayat_kelas rk ON n.siswa_id = rk.siswa_id
                    AND n.tahun_ajaran = rk.tahun_ajaran
                LEFT JOIN kelas k ON rk.kelas_id = k.id
                WHERE n.siswa_id = :siswa_id
                ORDER BY n.tahun_ajaran DESC, n.semester DESC, mp.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->execute();

        return $stmt;
    }

    public function getHistoricalGradesByNIS($nis) {
        $query = "SELECT n.*, mp.nama_mapel, mp.kkm, k.nama_kelas, rk.tahun_ajaran as kelas_tahun,
                        s.nama_siswa, s.status as student_status
                FROM " . $this->table_name . " n
                JOIN siswa s ON n.siswa_id = s.id
                JOIN mata_pelajaran mp ON n.mapel_id = mp.id
                LEFT JOIN riwayat_kelas rk ON n.siswa_id = rk.siswa_id
                    AND n.tahun_ajaran = rk.tahun_ajaran
                LEFT JOIN kelas k ON rk.kelas_id = k.id
                WHERE s.nis = :nis
                ORDER BY n.tahun_ajaran DESC, n.semester DESC, mp.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':nis', $nis);
        $stmt->execute();

        return $stmt;
    }
}
