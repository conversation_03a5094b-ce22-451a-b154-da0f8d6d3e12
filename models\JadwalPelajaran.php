<?php
require_once __DIR__ . '/../config/database.php';

class JadwalPelajaran {
    private $conn;
    private $table_name = "jadwal_pelajaran";

    public $id;
    public $kelas_id;
    public $mapel_id;
    public $hari;
    public $jam_mulai;
    public $jam_selesai;
    public $guru_id;
    public $semester;
    public $tahun_ajaran;
    public $nama_kelas;
    public $nama_mapel;
    public $tingkat_id;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getAll($semester = null, $tahun_ajaran = null) {
        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id";

        if ($semester && $tahun_ajaran) {
            $query .= " WHERE j.semester = :semester AND j.tahun_ajaran = :tahun_ajaran";
        }

        $query .= " ORDER BY j.hari ASC, j.jam_mulai ASC";

        $stmt = $this->conn->prepare($query);

        if ($semester && $tahun_ajaran) {
            $stmt->bindParam(":semester", $semester);
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getByKelas($kelas_id, $semester = null, $tahun_ajaran = null) {
        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id
                WHERE j.kelas_id = :kelas_id";

        if ($semester && $tahun_ajaran) {
            $query .= " AND j.semester = :semester AND j.tahun_ajaran = :tahun_ajaran";
        }

        $query .= " ORDER BY j.hari ASC, j.jam_mulai ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);

        if ($semester && $tahun_ajaran) {
            $stmt->bindParam(":semester", $semester);
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getCountByDay($day) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE hari = :hari";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":hari", $day);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getByDay($day) {
        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id
                WHERE j.hari = :hari
                ORDER BY j.jam_mulai ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":hari", $day);
        $stmt->execute();
        return $stmt;
    }

    public function getByMapel($mapel_id) {
        $query = "SELECT j.*, k.nama_kelas
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                WHERE j.mapel_id = :mapel_id
                ORDER BY k.nama_kelas ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->execute();
        return $stmt;
    }

    public function getKelasByMapel($mapel_id) {
        $query = "SELECT DISTINCT j.kelas_id, k.nama_kelas
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                WHERE j.mapel_id = :mapel_id
                ORDER BY k.nama_kelas ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->execute();
        return $stmt;
    }

    public function create() {
        // Get active period
        require_once __DIR__ . '/PeriodeAktif.php';
        $periode = new PeriodeAktif();
        if(!$periode->getActive()) {
            return false;
        }

        $query = "INSERT INTO jadwal_pelajaran
                  (kelas_id, mapel_id, hari, jam_mulai, jam_selesai, guru_id, tingkat_id, semester, tahun_ajaran)
                  VALUES
                  (:kelas_id, :mapel_id, :hari, :jam_mulai, :jam_selesai, :guru_id, :tingkat_id, :semester, :tahun_ajaran)";
        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
        $this->mapel_id = htmlspecialchars(strip_tags($this->mapel_id));
        $this->hari = htmlspecialchars(strip_tags($this->hari));
        $this->jam_mulai = htmlspecialchars(strip_tags($this->jam_mulai));
        $this->jam_selesai = htmlspecialchars(strip_tags($this->jam_selesai));
        $this->guru_id = htmlspecialchars(strip_tags($this->guru_id));
        $this->tingkat_id = htmlspecialchars(strip_tags($this->tingkat_id));

        $stmt->bindParam(':kelas_id', $this->kelas_id);
        $stmt->bindParam(':mapel_id', $this->mapel_id);
        $stmt->bindParam(':hari', $this->hari);
        $stmt->bindParam(':jam_mulai', $this->jam_mulai);
        $stmt->bindParam(':jam_selesai', $this->jam_selesai);
        $stmt->bindParam(':guru_id', $this->guru_id);
        $stmt->bindParam(':tingkat_id', $this->tingkat_id);
        $stmt->bindParam(':semester', $periode->semester);
        $stmt->bindParam(':tahun_ajaran', $periode->tahun_ajaran);

        if ($stmt->execute()) {
            $this->id = $this->conn->lastInsertId();
            return true;
        }
        return false;
    }

    public function getLastInsertedId() {
        return $this->id;
    }

    public function getJurusanIds($jadwal_id = null) {
        // Gunakan jadwal_id dari parameter atau dari instance
        $id = $jadwal_id ?? $this->id;

        $query = "SELECT jurusan_id FROM jadwal_jurusan WHERE jadwal_id = :jadwal_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':jadwal_id', $id);
        $stmt->execute();

        $ids = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $ids[] = $row['jurusan_id'];
        }
        return $ids;
    }

    public function getOne() {
        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru, g.id as guru_id
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id
                WHERE j.id = :id LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if($row) {
            $this->kelas_id = $row['kelas_id'];
            $this->mapel_id = $row['mapel_id'];
            $this->hari = $row['hari'];
            $this->jam_mulai = $row['jam_mulai'];
            $this->jam_selesai = $row['jam_selesai'];
            $this->guru_id = $row['guru_id'];
            $this->tingkat_id = $row['tingkat_id'];
            $this->nama_kelas = $row['nama_kelas'];
            $this->nama_mapel = $row['nama_mapel'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            return true;
        }
        return false;
    }

    public function update() {
        try {
            $query = "UPDATE " . $this->table_name . "
                    SET kelas_id = :kelas_id,
                        mapel_id = :mapel_id,
                        hari = :hari,
                        jam_mulai = :jam_mulai,
                        jam_selesai = :jam_selesai,
                        guru_id = :guru_id,
                        tingkat_id = :tingkat_id,
                        semester = :semester,
                        tahun_ajaran = :tahun_ajaran
                    WHERE id = :id";

            $stmt = $this->conn->prepare($query);

            // Sanitize input
            $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
            $this->mapel_id = htmlspecialchars(strip_tags($this->mapel_id));
            $this->hari = htmlspecialchars(strip_tags($this->hari));
            $this->jam_mulai = htmlspecialchars(strip_tags($this->jam_mulai));
            $this->jam_selesai = htmlspecialchars(strip_tags($this->jam_selesai));
            $this->guru_id = htmlspecialchars(strip_tags($this->guru_id));
            $this->semester = htmlspecialchars(strip_tags($this->semester));
            $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));
            $this->tingkat_id = htmlspecialchars(strip_tags($this->tingkat_id));
            $this->id = htmlspecialchars(strip_tags($this->id));

            // Bind parameters
            $stmt->bindParam(":kelas_id", $this->kelas_id);
            $stmt->bindParam(":mapel_id", $this->mapel_id);
            $stmt->bindParam(":hari", $this->hari);
            $stmt->bindParam(":jam_mulai", $this->jam_mulai);
            $stmt->bindParam(":jam_selesai", $this->jam_selesai);
            $stmt->bindParam(":guru_id", $this->guru_id);
            $stmt->bindParam(":semester", $this->semester);
            $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
            $stmt->bindParam(":tingkat_id", $this->tingkat_id);
            $stmt->bindParam(":id", $this->id);

            if (!$stmt->execute()) {
                throw new Exception("Gagal update jadwal: " . implode(", ", $stmt->errorInfo()));
            }

            return true;
        } catch (Exception $e) {
            throw new Exception("Gagal update jadwal: " . $e->getMessage());
        }
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function importFromArray($data) {
        $query = "INSERT INTO " . $this->table_name . "
                (kelas_id, mapel_id, hari, jam_mulai, jam_selesai, guru_id, semester, tahun_ajaran)
                VALUES (:kelas_id, :mapel_id, :hari, :jam_mulai, :jam_selesai, :guru_id, :semester, :tahun_ajaran)";

        $stmt = $this->conn->prepare($query);
        $success = true;

        foreach($data as $row) {
            // Sanitize input
            $kelas_id = htmlspecialchars(strip_tags($row['kelas_id']));
            $mapel_id = htmlspecialchars(strip_tags($row['mapel_id']));
            $hari = htmlspecialchars(strip_tags($row['hari']));
            $jam_mulai = htmlspecialchars(strip_tags($row['jam_mulai']));
            $jam_selesai = htmlspecialchars(strip_tags($row['jam_selesai']));
            $guru_id = isset($row['guru_id']) ? htmlspecialchars(strip_tags($row['guru_id'])) : null;
            $semester = htmlspecialchars(strip_tags($row['semester']));
            $tahun_ajaran = htmlspecialchars(strip_tags($row['tahun_ajaran']));

            // Bind parameters
            $stmt->bindParam(":kelas_id", $kelas_id);
            $stmt->bindParam(":mapel_id", $mapel_id);
            $stmt->bindParam(":hari", $hari);
            $stmt->bindParam(":jam_mulai", $jam_mulai);
            $stmt->bindParam(":jam_selesai", $jam_selesai);
            $stmt->bindParam(":guru_id", $guru_id);
            $stmt->bindParam(":semester", $semester);
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);

            if(!$stmt->execute()) {
                $success = false;
                break;
            }
        }
        return $success;
    }

    public function getByGuru($guru_id) {
        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id
                WHERE j.guru_id = :guru_id
                ORDER BY j.hari ASC, j.jam_mulai ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        return $stmt;
    }

    public function getByGuruAndDay($guru_id, $day) {
        $hari = is_numeric($day) ? $this->getHariFromNumber($day) : $day;
        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id
                WHERE j.guru_id = :guru_id AND j.hari = :hari
                ORDER BY j.jam_mulai ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->bindParam(":hari", $hari);
        $stmt->execute();
        return $stmt;
    }

    public function getByGuruId($guru_id, $semester = null, $tahun_ajaran = null) {
        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id
                WHERE j.guru_id = :guru_id";

        if ($semester && $tahun_ajaran) {
            $query .= " AND j.semester = :semester AND j.tahun_ajaran = :tahun_ajaran";
        }

        $query .= " ORDER BY FIELD(j.hari, 'Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'), j.jam_mulai ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);

        if ($semester && $tahun_ajaran) {
            $stmt->bindParam(":semester", $semester);
            $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getByMapelAndGuru($mapel_id, $guru_id) {
        $query = "SELECT * FROM " . $this->table_name . "
                WHERE mapel_id = :mapel_id
                AND guru_id = :guru_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        return $stmt;
    }

    public function getMapelByGuru($guru_id) {
        $query = "SELECT DISTINCT m.*
                FROM " . $this->table_name . " j
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                WHERE j.guru_id = :guru_id
                ORDER BY m.nama_mapel ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        return $stmt;
    }

    public function getMapelByKelas($kelas_id) {
        $query = "SELECT DISTINCT m.id, m.nama_mapel, m.kode_mapel
                FROM " . $this->table_name . " j
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                WHERE j.kelas_id = :kelas_id
                ORDER BY m.nama_mapel ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();
        return $stmt;
    }

    public function getMapelByKelasAndGuru($kelas_id, $guru_id) {
        $query = "SELECT DISTINCT m.id, m.nama_mapel, m.kode_mapel
                FROM " . $this->table_name . " j
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                WHERE j.kelas_id = :kelas_id AND j.guru_id = :guru_id
                ORDER BY m.nama_mapel ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();
        return $stmt;
    }

    public function getTodaySchedule($guru_id) {
        $hari = date('l');
        $hari_indo = [
            'Sunday' => 'Minggu',
            'Monday' => 'Senin',
            'Tuesday' => 'Selasa',
            'Wednesday' => 'Rabu',
            'Thursday' => 'Kamis',
            'Friday' => 'Jumat',
            'Saturday' => 'Sabtu'
        ];

        // Get active period
        require_once __DIR__ . '/PeriodeAktif.php';
        $periode = new PeriodeAktif();
        if (!$periode->getActive()) {
            // If no active period, return empty result
            $stmt = $this->conn->prepare("SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru FROM " . $this->table_name . " j LEFT JOIN kelas k ON j.kelas_id = k.id LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id LEFT JOIN guru g ON j.guru_id = g.id WHERE 1=0");
            $stmt->execute();
            return $stmt;
        }

        $query = "SELECT j.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                FROM " . $this->table_name . " j
                LEFT JOIN kelas k ON j.kelas_id = k.id
                LEFT JOIN mata_pelajaran m ON j.mapel_id = m.id
                LEFT JOIN guru g ON j.guru_id = g.id
                WHERE j.guru_id = :guru_id AND j.hari = :hari
                AND j.semester = :semester AND j.tahun_ajaran = :tahun_ajaran
                ORDER BY j.jam_mulai ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->bindParam(':hari', $hari_indo[$hari]);
        $stmt->bindParam(':semester', $periode->semester);
        $stmt->bindParam(':tahun_ajaran', $periode->tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function getAllTodaySchedule() {
        $today = date('l');
        $hari = $this->convertDayToIndonesian($today);

        // Get active period
        require_once __DIR__ . '/PeriodeAktif.php';
        $periode = new PeriodeAktif();
        if (!$periode->getActive()) {
            // If no active period, return empty result
            $stmt = $this->conn->prepare("SELECT jp.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru FROM " . $this->table_name . " jp JOIN kelas k ON jp.kelas_id = k.id JOIN mata_pelajaran m ON jp.mapel_id = m.id JOIN guru g ON jp.guru_id = g.id WHERE 1=0");
            $stmt->execute();
            return $stmt;
        }

        $query = "SELECT jp.*, k.nama_kelas, m.nama_mapel, g.nama_lengkap as nama_guru
                 FROM " . $this->table_name . " jp
                 JOIN kelas k ON jp.kelas_id = k.id
                 JOIN mata_pelajaran m ON jp.mapel_id = m.id
                 JOIN guru g ON jp.guru_id = g.id
                 WHERE jp.hari = :hari
                 AND jp.semester = :semester AND jp.tahun_ajaran = :tahun_ajaran
                 ORDER BY jp.jam_mulai ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":hari", $hari);
        $stmt->bindParam(':semester', $periode->semester);
        $stmt->bindParam(':tahun_ajaran', $periode->tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function checkConflicts($jadwal_id, $hari, $guru_id, $jam_ke_array) {
        $conflicts = [];
        $sql = "SELECT j.id, j.hari, j.guru_id, j.mapel_id, j.kelas_id, m.nama_mapel, k.nama_kelas,
                       GROUP_CONCAT(DISTINCT dj.jam_ke ORDER BY dj.jam_ke) as jam_ke,
                       GROUP_CONCAT(DISTINCT CONCAT(dj.jam_mulai, '-', dj.jam_selesai) ORDER BY dj.jam_ke) as jam_waktu
                FROM jadwal_pelajaran j
                JOIN detail_jadwal_jam dj ON j.id = dj.jadwal_id
                JOIN mata_pelajaran m ON j.mapel_id = m.id
                JOIN kelas k ON j.kelas_id = k.id
                WHERE j.id != ? AND j.hari = ? AND j.guru_id = ?
                GROUP BY j.id, j.hari, j.guru_id, j.mapel_id, j.kelas_id, m.nama_mapel, k.nama_kelas";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$jadwal_id, $hari, $guru_id]);

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $existing_jam = explode(',', $row['jam_ke']);
            foreach ($jam_ke_array as $new_jam) {
                if (in_array($new_jam, $existing_jam)) {
                    $conflicts[] = [
                        'jadwal_id' => $row['id'],
                        'mapel' => $row['nama_mapel'],
                        'kelas' => $row['nama_kelas'],
                        'jam_ke' => $row['jam_ke'],
                        'jam_waktu' => $row['jam_waktu']
                    ];
                    break;
                }
            }
        }

        return $conflicts;
    }

    public function getConflictingSchedules($jadwal_id) {
        $sql = "SELECT DISTINCT j2.id, j2.hari, m2.nama_mapel, k2.nama_kelas,
                       GROUP_CONCAT(DISTINCT dj2.jam_ke ORDER BY dj2.jam_ke) as jam_ke
                FROM jadwal_pelajaran j1
                JOIN detail_jadwal_jam dj1 ON j1.id = dj1.jadwal_id
                JOIN jadwal_pelajaran j2 ON j1.guru_id = j2.guru_id AND j1.hari = j2.hari AND j1.id != j2.id
                JOIN detail_jadwal_jam dj2 ON j2.id = dj2.jadwal_id
                JOIN mata_pelajaran m2 ON j2.mapel_id = m2.id
                JOIN kelas k2 ON j2.kelas_id = k2.id
                WHERE j1.id = ? AND dj1.jam_ke = dj2.jam_ke
                GROUP BY j2.id, j2.hari, m2.nama_mapel, k2.nama_kelas";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$jadwal_id]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    private function getHariFromNumber($number) {
        $hari = [
            1 => 'Senin',
            2 => 'Selasa',
            3 => 'Rabu',
            4 => 'Kamis',
            5 => 'Jumat',
            6 => 'Sabtu',
            7 => 'Minggu'
        ];
        return $hari[$number] ?? '';
    }

    private function convertDayToIndonesian($day) {
        $hari = [
            'Sunday' => 'Minggu',
            'Monday' => 'Senin',
            'Tuesday' => 'Selasa',
            'Wednesday' => 'Rabu',
            'Thursday' => 'Kamis',
            'Friday' => 'Jumat',
            'Saturday' => 'Sabtu'
        ];
        return $hari[$day];
    }

    public function getConnection() {
        return $this->conn;
    }

    public function saveJurusan($jadwal_id, $jurusan_id) {
        $query = "INSERT INTO jadwal_jurusan (jadwal_id, jurusan_id) VALUES (:jadwal_id, :jurusan_id)";
        $stmt = $this->conn->prepare($query);

        $stmt->bindParam(':jadwal_id', $jadwal_id);
        $stmt->bindParam(':jurusan_id', $jurusan_id);

        return $stmt->execute();
    }

    public function getKelasByMapelAndGuru($mapel_id, $guru_id) {
        $query = "SELECT DISTINCT k.*
                FROM kelas k
                INNER JOIN " . $this->table_name . " j ON k.id = j.kelas_id
                WHERE j.mapel_id = :mapel_id
                AND j.guru_id = :guru_id
                ORDER BY k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();
        return $stmt;
    }

    /**
     * Check if a teacher has access to a specific subject
     *
     * @param int $guru_id Teacher ID
     * @param int $mapel_id Subject ID
     * @return bool True if the teacher has access, false otherwise
     */
    public function checkGuruMapelAccess($guru_id, $mapel_id) {
        $query = "SELECT COUNT(*) as total
                FROM " . $this->table_name . "
                WHERE mapel_id = :mapel_id
                AND guru_id = :guru_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":guru_id", $guru_id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'] > 0;
    }
}
