<?php
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/TahunAjaran.php';
require_once '../models/Kelas.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../template/header.php';

// Get active period
$periode = new PeriodeAktif();
$periode->getActive();

// Get semester and tahun ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : null;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : null;
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';

// If no semester/tahun_ajaran selected, use active period
if ($semester === null || $tahun_ajaran === null) {
    $semester = $periode->semester;
    $tahun_ajaran = $periode->tahun_ajaran;
}

// Get available tahun ajaran
$ta = new TahunAjaran();
$tahun_ajaran_list = $ta->getAllAsArray();

// Get all classes
$kelas = new Kelas();
$kelas_list = $kelas->getAll();

// If no tahun ajaran exists, add current one
if(empty($tahun_ajaran_list)) {
    $ta->tahun_ajaran = $periode->tahun_ajaran;
    $ta->create();
    $tahun_ajaran_list = [$periode->tahun_ajaran];
}

// Get all mata pelajaran
$mapel = new MataPelajaran();
if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
    // Get guru_id from user_id
    $user = new User();
    $guru_id = $user->getGuruId($_SESSION['user_id']);

    if ($guru_id) {
        $result_mapel = $mapel->getByGuru($guru_id);
    } else {
        $result_mapel = $mapel->getAll();
    }
} else {
    $result_mapel = $mapel->getAll();
}

// Handle messages
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Data Nilai</h5>
            </div>
            <div class="card-body">
                <?php if($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Filter Data</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-3">
                                <label for="semester" class="form-label">Semester</label>
                                <select name="semester" id="semester" class="form-select">
                                    <option value="1" <?php echo $semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                                    <option value="2" <?php echo $semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> Anda dapat memilih semester apapun untuk input nilai
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                                <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                                    <?php foreach($tahun_ajaran_list as $ta): ?>
                                        <option value="<?php echo $ta['tahun_ajaran']; ?>" <?php echo $tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : ''; ?>>
                                            <?php echo $ta['tahun_ajaran']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i> Anda dapat memilih tahun ajaran apapun untuk input nilai
                                    <?php if (!isset($_SESSION['role']) || $_SESSION['role'] != 'guru'): ?>
                                    <br>
                                    <a href="/absen/tahun_ajaran" target="_blank">
                                        <i class="fas fa-plus"></i> Tambah Tahun Ajaran
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label for="kelas_id" class="form-label">Kelas</label>
                                <select name="kelas_id" id="kelas_id" class="form-select">
                                    <option value="">Semua Kelas</option>
                                    <?php while($row_kelas = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?php echo $row_kelas['id']; ?>" <?php echo $kelas_id == $row_kelas['id'] ? 'selected' : ''; ?>>
                                            <?php echo $row_kelas['nama_kelas']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button type="submit" class="btn btn-primary d-block w-100">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                                <?php if($semester != $periode->semester || $tahun_ajaran != $periode->tahun_ajaran): ?>
                                    <a href="index.php" class="btn btn-secondary d-block w-100 mt-2">
                                        <i class="fas fa-undo"></i> Kembali ke Periode Aktif
                                    </a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="card-title mb-0">Daftar Mata Pelajaran</h5>
                            <?php if($semester != $periode->semester || $tahun_ajaran != $periode->tahun_ajaran): ?>
                                <div class="alert alert-warning mt-2 mb-0">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    Anda sedang melihat periode: Semester <?php echo $semester; ?> - TA <?php echo $tahun_ajaran; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div>
                            <a href="history.php" class="btn btn-info btn-sm">
                                <i class="fas fa-history me-1"></i>Riwayat Nilai
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover" id="tableNilai">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>Kode Mapel</th>
                                        <th>Nama Mata Pelajaran</th>
                                        <th>Kelas</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $no = 1;
                                    while($row = $result_mapel->fetch(PDO::FETCH_ASSOC)) {
                                        // Get classes for this subject
                                        $jadwal = new JadwalPelajaran();
                                        $kelas_mapel = $jadwal->getKelasByMapel($row['id']);
                                        $kelas_names = [];
                                        while($row_kelas = $kelas_mapel->fetch(PDO::FETCH_ASSOC)) {
                                            if(empty($kelas_id) || $kelas_id == $row_kelas['kelas_id']) {
                                                $kelas_names[] = $row_kelas['nama_kelas'];
                                            }
                                        }

                                        // Skip if filtered by class and no match
                                        if(!empty($kelas_id) && empty($kelas_names)) {
                                            continue;
                                        }

                                        echo "<tr>";
                                        echo "<td>" . $no++ . "</td>";
                                        echo "<td>" . $row['kode_mapel'] . "</td>";
                                        echo "<td>" . $row['nama_mapel'] . "</td>";
                                        echo "<td>" . implode(", ", $kelas_names) . "</td>";
                                        echo "<td>
                                                <div class='btn-group' role='group'>
                                                    <a href='input.php?mapel_id=" . $row['id'] . "&semester=$semester&tahun_ajaran=$tahun_ajaran'
                                                       class='btn btn-primary btn-sm me-1' title='Input Nilai'>
                                                        <i class='fas fa-edit'></i> Input Nilai
                                                    </a>
                                                    <a href='view.php?mapel_id=" . $row['id'] . "&semester=$semester&tahun_ajaran=$tahun_ajaran'
                                                       class='btn btn-info btn-sm' title='Lihat Nilai'>
                                                        <i class='fas fa-eye'></i> Lihat Nilai
                                                    </a>
                                                </div>
                                              </td>";
                                        echo "</tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableNilai').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
