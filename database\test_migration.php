<?php
/**
 * Test script for database migrations
 * This script tests the migration functionality without requiring web interface
 */

require_once __DIR__ . '/../config/database.php';

function testExpectedAnswerMigration() {
    echo "Testing Expected Answer Migration...\n";
    echo "===================================\n\n";
    
    try {
        $database = new Database();
        $conn = $database->getConnection();
        
        // Function to check if column exists
        function checkColumnExists($conn, $table_name, $column_name) {
            try {
                $query = "SELECT COUNT(*) as count 
                         FROM INFORMATION_SCHEMA.COLUMNS 
                         WHERE TABLE_SCHEMA = DATABASE() 
                         AND TABLE_NAME = :table_name 
                         AND COLUMN_NAME = :column_name";
                
                $stmt = $conn->prepare($query);
                $stmt->bindParam(':table_name', $table_name);
                $stmt->bindParam(':column_name', $column_name);
                $stmt->execute();
                
                $result = $stmt->fetch(PDO::FETCH_ASSOC);
                return $result['count'] > 0;
                
            } catch (Exception $e) {
                echo "Column check error: " . $e->getMessage() . "\n";
                return false;
            }
        }
        
        // Test migration logic
        $missing_columns = [];
        $migration_success = true;
        $migration_messages = [];
        
        // Check and migrate rpp_questions table
        echo "Checking rpp_questions table...\n";
        if (!checkColumnExists($conn, 'rpp_questions', 'expected_answer')) {
            echo "- Column expected_answer missing, attempting to add...\n";
            try {
                $sql = "ALTER TABLE `rpp_questions` ADD COLUMN `expected_answer` TEXT DEFAULT NULL AFTER `correct_answer`";
                $conn->exec($sql);
                $migration_messages[] = "Added expected_answer column to rpp_questions table";
                echo "- ✓ Successfully added expected_answer column\n";
            } catch (Exception $e) {
                $migration_success = false;
                $migration_messages[] = "Error migrating rpp_questions: " . $e->getMessage();
                echo "- ✗ Error: " . $e->getMessage() . "\n";
            }
        } else {
            $migration_messages[] = "Column expected_answer already exists in rpp_questions table";
            echo "- ✓ Column expected_answer already exists\n";
        }
        
        // Check and migrate multi_rpp_exam_questions table
        echo "\nChecking multi_rpp_exam_questions table...\n";
        if (!checkColumnExists($conn, 'multi_rpp_exam_questions', 'expected_answer')) {
            echo "- Column expected_answer missing, attempting to add...\n";
            try {
                $sql = "ALTER TABLE `multi_rpp_exam_questions` ADD COLUMN `expected_answer` TEXT DEFAULT NULL AFTER `correct_answer`";
                $conn->exec($sql);
                $migration_messages[] = "Added expected_answer column to multi_rpp_exam_questions table";
                echo "- ✓ Successfully added expected_answer column\n";
            } catch (Exception $e) {
                $migration_success = false;
                $migration_messages[] = "Error migrating multi_rpp_exam_questions: " . $e->getMessage();
                echo "- ✗ Error: " . $e->getMessage() . "\n";
            }
        } else {
            $migration_messages[] = "Column expected_answer already exists in multi_rpp_exam_questions table";
            echo "- ✓ Column expected_answer already exists\n";
        }
        
        // Final result
        echo "\n" . str_repeat("=", 50) . "\n";
        if ($migration_success) {
            echo "✓ MIGRATION TEST PASSED\n";
            echo "Result: " . implode(". ", $migration_messages) . "\n";
        } else {
            echo "✗ MIGRATION TEST FAILED\n";
            echo "Errors: " . implode(". ", $migration_messages) . "\n";
        }
        echo str_repeat("=", 50) . "\n";
        
        return $migration_success;
        
    } catch (Exception $e) {
        echo "✗ MIGRATION TEST FAILED\n";
        echo "Database connection error: " . $e->getMessage() . "\n";
        return false;
    }
}

// Run the test
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $success = testExpectedAnswerMigration();
    exit($success ? 0 : 1);
}
?>
