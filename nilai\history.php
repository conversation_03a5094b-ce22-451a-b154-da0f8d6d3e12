<?php
require_once '../middleware/auth.php';
checkAuth();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/Nilai.php';
require_once '../models/Siswa.php';
require_once '../models/Alumni.php';
require_once '../models/User.php';

$nilaiModel = new Nilai();
$siswaModel = new Siswa();
$alumniModel = new Alumni();
$userModel = new User();

// Check if user has access
$is_admin = $_SESSION['role'] === 'admin';
$guru_id = null;
if ($_SESSION['role'] === 'guru') {
    $guru_id = $userModel->getGuruId($_SESSION['user_id']);
}

$search_nis = isset($_GET['nis']) ? trim($_GET['nis']) : '';
$student_data = null;
$grade_history = [];
$class_history = [];
$error_message = '';

if ($search_nis) {
    // First check if student is active
    $student_data = $siswaModel->getByNIS($search_nis);
    
    if (!$student_data) {
        // Check if student is alumni
        $alumni_data = $alumniModel->getAlumniByNIS($search_nis);
        if ($alumni_data) {
            $student_data = [
                'id' => $alumni_data['siswa_id'],
                'nis' => $alumni_data['nis'],
                'nama_siswa' => $alumni_data['nama_siswa'],
                'jenis_kelamin' => $alumni_data['jenis_kelamin'],
                'alamat' => $alumni_data['alamat'],
                'no_telp' => $alumni_data['no_telp'],
                'status' => 'alumni',
                'tahun_lulus' => $alumni_data['tahun_lulus'],
                'kelas_terakhir' => $alumni_data['kelas_terakhir']
            ];
        }
    } else {
        $student_data['status'] = $student_data['status'] ?? 'aktif';
    }
    
    if ($student_data) {
        // Get grade history
        $grade_history_stmt = $nilaiModel->getHistoricalGradesByNIS($search_nis);
        $grade_history = $grade_history_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Get class history if alumni
        if ($student_data['status'] === 'alumni' && isset($alumni_data)) {
            $class_history_stmt = $alumniModel->getAlumniClassHistory($alumni_data['id']);
            $class_history = $class_history_stmt;
        }
    } else {
        $error_message = "Siswa dengan NIS '$search_nis' tidak ditemukan.";
    }
}

// Group grades by academic year and semester
$grouped_grades = [];
foreach ($grade_history as $grade) {
    $key = $grade['tahun_ajaran'] . '_' . $grade['semester'];
    if (!isset($grouped_grades[$key])) {
        $grouped_grades[$key] = [
            'tahun_ajaran' => $grade['tahun_ajaran'],
            'semester' => $grade['semester'],
            'kelas' => $grade['nama_kelas'] ?? 'N/A',
            'grades' => []
        ];
    }
    $grouped_grades[$key]['grades'][] = $grade;
}

// Sort by academic year and semester (newest first)
uksort($grouped_grades, function($a, $b) {
    $a_parts = explode('_', $a);
    $b_parts = explode('_', $b);
    
    // Compare academic year first
    $year_compare = strcmp($b_parts[0], $a_parts[0]);
    if ($year_compare !== 0) {
        return $year_compare;
    }
    
    // Then compare semester (2 before 1)
    return $b_parts[1] - $a_parts[1];
});
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>Riwayat Nilai Siswa
                    </h5>
                    <a href="index.php" class="btn btn-secondary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Kembali
                    </a>
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="nis" 
                                           placeholder="Masukkan NIS siswa..." 
                                           value="<?php echo htmlspecialchars($search_nis); ?>" required>
                                    <button class="btn btn-primary" type="submit">
                                        <i class="fas fa-search me-1"></i>Cari
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <?php if ($error_message): ?>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i><?php echo $error_message; ?>
                        </div>
                    <?php endif; ?>

                    <?php if ($student_data): ?>
                        <!-- Student Information -->
                        <div class="card mb-4">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-user me-2"></i>Informasi Siswa
                                    <?php if ($student_data['status'] === 'alumni'): ?>
                                        <span class="badge bg-success ms-2">Alumni</span>
                                    <?php else: ?>
                                        <span class="badge bg-primary ms-2">Aktif</span>
                                    <?php endif; ?>
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <tr>
                                                <td width="120"><strong>NIS</strong></td>
                                                <td>: <?php echo htmlspecialchars($student_data['nis']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Nama</strong></td>
                                                <td>: <?php echo htmlspecialchars($student_data['nama_siswa']); ?></td>
                                            </tr>
                                            <tr>
                                                <td><strong>Jenis Kelamin</strong></td>
                                                <td>: <?php echo $student_data['jenis_kelamin'] === 'L' ? 'Laki-laki' : 'Perempuan'; ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless table-sm">
                                            <?php if ($student_data['status'] === 'alumni'): ?>
                                                <tr>
                                                    <td width="120"><strong>Tahun Lulus</strong></td>
                                                    <td>: <?php echo htmlspecialchars($student_data['tahun_lulus']); ?></td>
                                                </tr>
                                                <tr>
                                                    <td><strong>Kelas Terakhir</strong></td>
                                                    <td>: <?php echo htmlspecialchars($student_data['kelas_terakhir']); ?></td>
                                                </tr>
                                            <?php endif; ?>
                                            <tr>
                                                <td><strong>Status</strong></td>
                                                <td>: <?php echo ucfirst($student_data['status']); ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Grade History -->
                        <?php if (!empty($grouped_grades)): ?>
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-chart-line me-2"></i>Riwayat Nilai Akademik
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <?php foreach ($grouped_grades as $period): ?>
                                        <div class="mb-4">
                                            <h6 class="text-primary border-bottom pb-2">
                                                <i class="fas fa-calendar me-2"></i>
                                                Semester <?php echo $period['semester']; ?> - 
                                                Tahun Ajaran <?php echo $period['tahun_ajaran']; ?>
                                                <small class="text-muted ms-2">(Kelas: <?php echo $period['kelas']; ?>)</small>
                                            </h6>
                                            
                                            <div class="table-responsive">
                                                <table class="table table-striped table-hover table-sm">
                                                    <thead class="table-dark">
                                                        <tr>
                                                            <th>No</th>
                                                            <th>Mata Pelajaran</th>
                                                            <th>Tugas</th>
                                                            <th>UTS</th>
                                                            <th>UAS</th>
                                                            <th>Absensi</th>
                                                            <th>Nilai Akhir</th>
                                                            <th>KKM</th>
                                                            <th>Status</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php 
                                                        $no = 1;
                                                        foreach ($period['grades'] as $grade): 
                                                            $status = ($grade['nilai_akhir'] ?? 0) >= ($grade['kkm'] ?? 75) ? 'Tuntas' : 'Belum Tuntas';
                                                            $status_class = ($grade['nilai_akhir'] ?? 0) >= ($grade['kkm'] ?? 75) ? 'success' : 'danger';
                                                        ?>
                                                            <tr>
                                                                <td><?php echo $no++; ?></td>
                                                                <td><?php echo htmlspecialchars($grade['nama_mapel']); ?></td>
                                                                <td><?php echo $grade['nilai_tugas'] ? number_format($grade['nilai_tugas'], 2) : '-'; ?></td>
                                                                <td><?php echo $grade['nilai_uts'] ? number_format($grade['nilai_uts'], 2) : '-'; ?></td>
                                                                <td><?php echo $grade['nilai_uas'] ? number_format($grade['nilai_uas'], 2) : '-'; ?></td>
                                                                <td><?php echo $grade['nilai_absen'] ? number_format($grade['nilai_absen'], 2) : '-'; ?></td>
                                                                <td>
                                                                    <strong><?php echo $grade['nilai_akhir'] ? number_format($grade['nilai_akhir'], 2) : '-'; ?></strong>
                                                                </td>
                                                                <td><?php echo number_format($grade['kkm'] ?? 75, 0); ?></td>
                                                                <td>
                                                                    <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status; ?></span>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                Tidak ada data nilai ditemukan untuk siswa ini.
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>

<script>
$(document).ready(function() {
    // Initialize DataTables for better table handling
    $('.table').each(function() {
        if ($(this).find('tbody tr').length > 0) {
            $(this).DataTable({
                "pageLength": 25,
                "ordering": false,
                "searching": false,
                "lengthChange": false,
                "info": false,
                "paging": false
            });
        }
    });
});
</script>
