<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess(); // Only admin can run database migrations
require_once '../config/database.php';

// Initialize variables
$missing_columns = [];
$migration_status = '';
$error_message = '';
$success_message = '';

// Check if migration is being executed
if (isset($_POST['execute_migration'])) {
    try {
        $database = new Database();
        $conn = $database->getConnection();

        $migration_success = true;
        $migration_messages = [];

        // Execute migration for rpp_questions table
        try {
            if (!checkColumnExists($conn, 'rpp_questions', 'expected_answer')) {
                $sql = "ALTER TABLE `rpp_questions` ADD COLUMN `expected_answer` TEXT DEFAULT NULL AFTER `correct_answer`";
                $conn->exec($sql);
                $migration_messages[] = "Added expected_answer column to rpp_questions table";
            } else {
                $migration_messages[] = "Column expected_answer already exists in rpp_questions table";
            }
        } catch (Exception $e) {
            $migration_success = false;
            $migration_messages[] = "Error migrating rpp_questions: " . $e->getMessage();
        }

        // Execute migration for multi_rpp_exam_questions table
        try {
            if (!checkColumnExists($conn, 'multi_rpp_exam_questions', 'expected_answer')) {
                $sql = "ALTER TABLE `multi_rpp_exam_questions` ADD COLUMN `expected_answer` TEXT DEFAULT NULL AFTER `correct_answer`";
                $conn->exec($sql);
                $migration_messages[] = "Added expected_answer column to multi_rpp_exam_questions table";
            } else {
                $migration_messages[] = "Column expected_answer already exists in multi_rpp_exam_questions table";
            }
        } catch (Exception $e) {
            $migration_success = false;
            $migration_messages[] = "Error migrating multi_rpp_exam_questions: " . $e->getMessage();
        }

        if ($migration_success) {
            $success_message = "Database migration completed successfully! " . implode(". ", $migration_messages);
        } else {
            $error_message = "Migration completed with errors: " . implode(". ", $migration_messages);
        }

    } catch (Exception $e) {
        $error_message = "Migration failed: " . $e->getMessage();
        error_log("Database Migration Error: " . $e->getMessage());
    }
}

// Function to check if column exists in table
function checkColumnExists($conn, $table_name, $column_name) {
    try {
        $query = "SELECT COUNT(*) as count 
                 FROM INFORMATION_SCHEMA.COLUMNS 
                 WHERE TABLE_SCHEMA = DATABASE() 
                 AND TABLE_NAME = :table_name 
                 AND COLUMN_NAME = :column_name";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':table_name', $table_name);
        $stmt->bindParam(':column_name', $column_name);
        $stmt->execute();
        
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result['count'] > 0;
        
    } catch (Exception $e) {
        error_log("Column check error: " . $e->getMessage());
        return false;
    }
}

// Check for missing columns
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Check rpp_questions table
    if (!checkColumnExists($conn, 'rpp_questions', 'expected_answer')) {
        $missing_columns[] = [
            'table' => 'rpp_questions',
            'column' => 'expected_answer',
            'description' => 'RPP Questions table - stores expected answers for essay questions'
        ];
    }
    
    // Check multi_rpp_exam_questions table
    if (!checkColumnExists($conn, 'multi_rpp_exam_questions', 'expected_answer')) {
        $missing_columns[] = [
            'table' => 'multi_rpp_exam_questions',
            'column' => 'expected_answer',
            'description' => 'Multi-RPP Exam Questions table - stores expected answers for multi-RPP essay questions'
        ];
    }
    
} catch (Exception $e) {
    $error_message = "Database connection error: " . $e->getMessage();
    error_log("Database Check Error: " . $e->getMessage());
}

// Get page title
$page_title = "Database Migration - Expected Answer Columns";
?>

<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= $page_title ?> - SIHADIR</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .migration-card {
            border-left: 4px solid #ffc107;
            background: #fff3cd;
        }
        .success-card {
            border-left: 4px solid #198754;
            background: #d1e7dd;
        }
        .error-card {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
        .table-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .migration-btn {
            background: linear-gradient(45deg, #ffc107, #fd7e14);
            border: none;
            color: white;
            font-weight: bold;
            padding: 12px 30px;
            border-radius: 25px;
            transition: all 0.3s ease;
        }
        .migration-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 193, 7, 0.4);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <!-- Header -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database"></i> Database Migration Check
                        </h4>
                        <small>Expected Answer Columns Migration Status</small>
                    </div>
                </div>

                <!-- Success Message -->
                <?php if (!empty($success_message)): ?>
                    <div class="card success-card shadow-sm mb-4">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success fa-2x me-3"></i>
                                <div>
                                    <h5 class="text-success mb-1">Migration Successful!</h5>
                                    <p class="mb-0"><?= htmlspecialchars($success_message) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Error Message -->
                <?php if (!empty($error_message)): ?>
                    <div class="card error-card shadow-sm mb-4">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-exclamation-triangle text-danger fa-2x me-3"></i>
                                <div>
                                    <h5 class="text-danger mb-1">Migration Error!</h5>
                                    <p class="mb-0"><?= htmlspecialchars($error_message) ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Migration Required Warning -->
                <?php if (!empty($missing_columns) && empty($success_message)): ?>
                    <div class="card migration-card shadow-sm mb-4">
                        <div class="card-body">
                            <div class="d-flex align-items-start">
                                <i class="fas fa-exclamation-triangle text-warning fa-2x me-3 mt-1"></i>
                                <div class="flex-grow-1">
                                    <h5 class="text-warning mb-3">Database Migration Required!</h5>
                                    <p class="mb-3">
                                        The following database columns are missing and need to be added to support 
                                        the new <strong>Expected Answer</strong> functionality for essay questions:
                                    </p>
                                    
                                    <!-- Missing Columns List -->
                                    <?php foreach ($missing_columns as $column): ?>
                                        <div class="table-info">
                                            <div class="row align-items-center">
                                                <div class="col-md-3">
                                                    <strong>Table:</strong><br>
                                                    <code><?= htmlspecialchars($column['table']) ?></code>
                                                </div>
                                                <div class="col-md-3">
                                                    <strong>Column:</strong><br>
                                                    <code><?= htmlspecialchars($column['column']) ?></code>
                                                </div>
                                                <div class="col-md-6">
                                                    <strong>Purpose:</strong><br>
                                                    <?= htmlspecialchars($column['description']) ?>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                    
                                    <!-- Migration Information -->
                                    <div class="alert alert-info mt-3">
                                        <h6><i class="fas fa-info-circle"></i> Migration Details:</h6>
                                        <ul class="mb-0">
                                            <li>This migration is <strong>safe</strong> and will not remove or modify existing data</li>
                                            <li>The migration is <strong>idempotent</strong> - safe to run multiple times</li>
                                            <li>New columns will be added with <code>DEFAULT NULL</code> values</li>
                                            <li>Existing functionality will continue to work normally</li>
                                        </ul>
                                    </div>
                                    
                                    <!-- Migration Button -->
                                    <form method="POST" class="mt-4">
                                        <button type="submit" name="execute_migration" class="btn migration-btn btn-lg" 
                                                onclick="return confirm('Are you sure you want to execute the database migration? This will add the missing expected_answer columns to the database.')">
                                            <i class="fas fa-play-circle me-2"></i>
                                            Execute Database Migration
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php elseif (empty($missing_columns)): ?>
                    <!-- All Good Message -->
                    <div class="card success-card shadow-sm mb-4">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-check-circle text-success fa-2x me-3"></i>
                                <div>
                                    <h5 class="text-success mb-1">Database is Up to Date!</h5>
                                    <p class="mb-0">
                                        All required columns for the Expected Answer functionality are present in the database. 
                                        No migration is needed.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Back Button -->
                <div class="text-center">
                    <a href="../admin/" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Admin Panel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
