# Grade History Preservation Migration

This document provides instructions for running the grade history preservation migration and fixing related constraint issues.

## Overview

The grade history preservation migration ensures that student academic records are preserved when students are promoted or graduated. This migration also fixes critical foreign key constraint issues that prevent the graduation process.

## Critical Fix Included

**Problem**: The original database schema had a foreign key constraint that prevented students from being graduated:
```
SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: 
a foreign key constraint fails (`db_absensi`.`siswa`, CONSTRAINT `siswa_ibfk_1` 
FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE CASCADE)
```

**Solution**: The migration now automatically fixes this by:
1. Changing `siswa.kelas_id` from NOT NULL to NULL
2. Updating the foreign key constraint from `ON DELETE CASCADE` to `ON DELETE SET NULL`
3. Allowing the graduation process to set `kelas_id = NULL` without errors

## Migration Files

### 1. `migrate_grade_history.php`
- **Main migration script** with automatic constraint fixing
- Preserves all student grade history
- Adds status tracking for students
- Creates alumni-student relationships
- **Now includes automatic constraint fixing**

### 2. `fix_siswa_constraints.sql`
- **Standalone constraint fix** (if needed separately)
- Can be used for manual constraint fixing

### 3. `grade_history_preservation.sql`
- **SQL schema definitions** for the migration
- Contains all table modifications and view definitions

## Pre-Migration Checklist

- [ ] **Backup your database** (CRITICAL!)
- [ ] Verify PHP PDO extension is installed
- [ ] Check database user has ALTER privileges
- [ ] Test on a copy of production data first

## Running the Migration

### Step 1: Backup Database
```bash
mysqldump -u username -p db_absensi > backup_$(date +%Y%m%d_%H%M%S).sql
```

### Step 2: Run Migration
```bash
cd /path/to/absen
php database/migrations/migrate_grade_history.php
```

### Step 3: Verify Success
The migration will output progress messages. Look for:
- ✓ All steps completed without errors
- ✓ Constraint fixing completed successfully
- ✓ Foreign key relationships established

## Verification Commands

### Check if kelas_id allows NULL:
```sql
SELECT IS_NULLABLE 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'db_absensi' 
AND TABLE_NAME = 'siswa' 
AND COLUMN_NAME = 'kelas_id';
```
**Expected Result**: `YES`

### Check constraint details:
```sql
SELECT 
    kcu.CONSTRAINT_NAME,
    rc.DELETE_RULE,
    rc.UPDATE_RULE
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
    ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
WHERE kcu.TABLE_SCHEMA = 'db_absensi' 
AND kcu.TABLE_NAME = 'siswa' 
AND kcu.COLUMN_NAME = 'kelas_id';
```
**Expected Result**: `DELETE_RULE = 'SET NULL'`

### Test graduation process:
```sql
-- This should now work without errors
UPDATE siswa SET status = 'alumni', kelas_id = NULL WHERE id = 1;
-- Rollback the test
UPDATE siswa SET status = 'aktif', kelas_id = 1 WHERE id = 1;
```

## Manual Constraint Fix (if needed)

If the automatic fix didn't work, run these commands manually:

```sql
-- Drop old constraint
ALTER TABLE `siswa` DROP FOREIGN KEY `siswa_ibfk_1`;

-- Allow NULL values
ALTER TABLE `siswa` MODIFY COLUMN `kelas_id` INT(11) NULL;

-- Add new constraint with SET NULL
ALTER TABLE `siswa` ADD CONSTRAINT `siswa_kelas_fk` 
FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;
```

## Testing the Fix

### Test Graduation Process:
1. Go to **Siswa** → **Naik Kelas/Kelulusan**
2. Select a student for graduation
3. The process should complete without foreign key errors
4. Verify the student's `kelas_id` is set to NULL
5. Verify the student's status is set to 'alumni'

### Test Promotion Process:
1. Go to **Siswa** → **Naik Kelas/Kelulusan**
2. Select students for promotion to a new class
3. The process should complete successfully
4. Verify students are moved to the new class
5. Verify class history is recorded

## Rollback Instructions

If you need to rollback:

### Option 1: Restore from Backup
```bash
mysql -u username -p db_absensi < backup_file.sql
```

### Option 2: Manual Rollback
```sql
-- Remove new columns
ALTER TABLE siswa DROP COLUMN status;
ALTER TABLE siswa DROP COLUMN alumni_id;
ALTER TABLE alumni DROP COLUMN siswa_id;

-- Restore original constraint (WARNING: This will prevent graduation!)
ALTER TABLE siswa DROP FOREIGN KEY siswa_kelas_fk;
ALTER TABLE siswa MODIFY COLUMN kelas_id INT(11) NOT NULL;
ALTER TABLE siswa ADD CONSTRAINT siswa_ibfk_1 
FOREIGN KEY (kelas_id) REFERENCES kelas (id) ON DELETE CASCADE;
```

## Troubleshooting

### Migration Fails with Permission Error:
- Ensure database user has ALTER, CREATE, DROP privileges
- Check if other processes are using the database

### Constraint Still Causing Issues:
- Verify the constraint was actually updated
- Check for multiple constraints on the same column
- Run verification commands above

### Data Integrity Issues:
- Restore from backup
- Check for orphaned records before re-running migration
- Verify all referenced tables exist

## What This Migration Adds

### New Columns:
- `siswa.status` - Tracks student status ('aktif', 'alumni', 'pindah')
- `siswa.alumni_id` - References alumni record when graduated
- `alumni.siswa_id` - References preserved student record

### Modified Constraints:
- `siswa.kelas_id` - Now allows NULL and uses SET NULL on delete
- All grade-related tables - Use RESTRICT to prevent data loss

### New Views:
- `v_student_academic_record` - Complete student academic history
- `v_alumni_academic_record` - Complete alumni academic history

## Benefits After Migration

1. **No More Graduation Errors**: Students can be graduated without constraint violations
2. **Preserved Grade History**: All academic records are maintained permanently
3. **Alumni Management**: Complete academic records for graduated students
4. **Data Integrity**: Referential integrity maintained while preventing data loss
5. **Historical Access**: Teachers can access complete student academic history

## Support

For issues or questions:
1. Check migration output for specific error messages
2. Verify database permissions and PHP configuration
3. Test with verification commands provided above
4. Restore from backup if needed and retry

---

**Important**: Always backup your database before running any migration!
