<?php
/**
 * Test Script for Grade History Preservation Migration
 * 
 * This script tests the migration process and verifies that grade history
 * is preserved correctly when students are promoted or moved to alumni status.
 */

require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../models/Siswa.php';
require_once __DIR__ . '/../../models/Alumni.php';
require_once __DIR__ . '/../../models/Nilai.php';
require_once __DIR__ . '/../../models/Kelas.php';

class MigrationTest {
    private $conn;
    private $siswaModel;
    private $alumniModel;
    private $nilaiModel;
    private $kelasModel;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->siswaModel = new Siswa();
        $this->alumniModel = new Alumni();
        $this->nilaiModel = new Nilai();
        $this->kelasModel = new Kelas();
    }
    
    public function runTests() {
        echo "Starting Grade History Preservation Tests...\n";
        echo "==========================================\n\n";
        
        $tests = [
            'testDatabaseStructure',
            'testGradeHistoryRetrieval',
            'testAlumniGradeAccess',
            'testStudentPromotion',
            'testDataConsistency'
        ];
        
        $passed = 0;
        $total = count($tests);
        
        foreach ($tests as $test) {
            echo "Running {$test}... ";
            try {
                if ($this->$test()) {
                    echo "PASSED\n";
                    $passed++;
                } else {
                    echo "FAILED\n";
                }
            } catch (Exception $e) {
                echo "ERROR: " . $e->getMessage() . "\n";
            }
        }
        
        echo "\n==========================================\n";
        echo "Test Results: {$passed}/{$total} tests passed\n";
        
        return $passed === $total;
    }
    
    private function testDatabaseStructure() {
        // Test if new columns exist
        $tables_to_check = [
            'alumni' => ['siswa_id'],
            'siswa' => ['status', 'alumni_id']
        ];
        
        foreach ($tables_to_check as $table => $columns) {
            foreach ($columns as $column) {
                $stmt = $this->conn->prepare("SHOW COLUMNS FROM {$table} LIKE '{$column}'");
                $stmt->execute();
                if ($stmt->rowCount() === 0) {
                    throw new Exception("Column {$column} not found in table {$table}");
                }
            }
        }
        
        // Test if views exist
        $views = ['v_student_academic_record', 'v_alumni_academic_record'];
        foreach ($views as $view) {
            $stmt = $this->conn->prepare("SHOW TABLES LIKE '{$view}'");
            $stmt->execute();
            if ($stmt->rowCount() === 0) {
                throw new Exception("View {$view} not found");
            }
        }
        
        return true;
    }
    
    private function testGradeHistoryRetrieval() {
        // Test if we can retrieve historical grades
        $stmt = $this->conn->query("SELECT s.nis FROM siswa s LIMIT 1");
        $student = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($student) {
            $grades = $this->nilaiModel->getHistoricalGradesByNIS($student['nis']);
            // Should return a PDOStatement
            return $grades instanceof PDOStatement;
        }
        
        return true; // No students to test with
    }
    
    private function testAlumniGradeAccess() {
        // Test if alumni can access their grades
        $stmt = $this->conn->query("SELECT id FROM alumni LIMIT 1");
        $alumni = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($alumni) {
            $grades = $this->alumniModel->getAlumniWithGrades($alumni['id']);
            // Should return an array
            return is_array($grades);
        }
        
        return true; // No alumni to test with
    }
    
    private function testStudentPromotion() {
        // Test if student promotion preserves data
        // This is a read-only test - we don't actually promote students
        
        // Check if there are any students with grades
        $stmt = $this->conn->query("
            SELECT s.id, s.nis, COUNT(n.id) as grade_count 
            FROM siswa s 
            LEFT JOIN nilai n ON s.id = n.siswa_id 
            WHERE s.status = 'aktif'
            GROUP BY s.id 
            HAVING grade_count > 0 
            LIMIT 1
        ");
        
        $student = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($student) {
            // Verify that grades exist for this student
            $grades = $this->nilaiModel->getHistoricalGradesBySiswaId($student['id']);
            $grade_records = $grades->fetchAll(PDO::FETCH_ASSOC);
            
            return count($grade_records) > 0;
        }
        
        return true; // No students with grades to test
    }
    
    private function testDataConsistency() {
        // Test data consistency between tables
        
        // Check if all alumni have corresponding siswa records
        $stmt = $this->conn->query("
            SELECT COUNT(*) as count 
            FROM alumni a 
            LEFT JOIN siswa s ON a.siswa_id = s.id 
            WHERE a.siswa_id IS NOT NULL AND s.id IS NULL
        ");
        $orphaned_alumni = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($orphaned_alumni > 0) {
            throw new Exception("Found {$orphaned_alumni} alumni records without corresponding siswa records");
        }
        
        // Check if all siswa with alumni status have corresponding alumni records
        $stmt = $this->conn->query("
            SELECT COUNT(*) as count 
            FROM siswa s 
            LEFT JOIN alumni a ON s.alumni_id = a.id 
            WHERE s.status = 'alumni' AND s.alumni_id IS NOT NULL AND a.id IS NULL
        ");
        $orphaned_students = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($orphaned_students > 0) {
            throw new Exception("Found {$orphaned_students} alumni students without corresponding alumni records");
        }
        
        return true;
    }
    
    public function generateTestReport() {
        echo "\nGenerating Test Report...\n";
        echo "========================\n\n";
        
        // Count active students
        $stmt = $this->conn->query("SELECT COUNT(*) as count FROM siswa WHERE status = 'aktif'");
        $active_students = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count alumni students
        $stmt = $this->conn->query("SELECT COUNT(*) as count FROM siswa WHERE status = 'alumni'");
        $alumni_students = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count alumni records
        $stmt = $this->conn->query("SELECT COUNT(*) as count FROM alumni");
        $alumni_records = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count grade records
        $stmt = $this->conn->query("SELECT COUNT(*) as count FROM nilai");
        $grade_records = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        // Count students with grades
        $stmt = $this->conn->query("
            SELECT COUNT(DISTINCT siswa_id) as count 
            FROM nilai n 
            JOIN siswa s ON n.siswa_id = s.id
        ");
        $students_with_grades = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        echo "Database Statistics:\n";
        echo "- Active Students: {$active_students}\n";
        echo "- Alumni Students: {$alumni_students}\n";
        echo "- Alumni Records: {$alumni_records}\n";
        echo "- Grade Records: {$grade_records}\n";
        echo "- Students with Grades: {$students_with_grades}\n\n";
        
        // Test grade accessibility
        echo "Grade Accessibility Test:\n";
        $stmt = $this->conn->query("
            SELECT 
                s.status,
                COUNT(DISTINCT s.id) as student_count,
                COUNT(n.id) as grade_count
            FROM siswa s
            LEFT JOIN nilai n ON s.id = n.siswa_id
            GROUP BY s.status
        ");
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['status']} students: {$row['student_count']} students, {$row['grade_count']} grades\n";
        }
        
        echo "\nMigration Status: ";
        if ($alumni_students > 0 && $grade_records > 0) {
            echo "SUCCESS - Grade history preservation is working\n";
        } else {
            echo "PENDING - No alumni students or grades to test with\n";
        }
    }
}

// Run tests if called directly
if (basename(__FILE__) == basename($_SERVER['SCRIPT_NAME'])) {
    $test = new MigrationTest();
    $success = $test->runTests();
    $test->generateTestReport();
    
    if ($success) {
        echo "\nAll tests passed! Grade history preservation is working correctly.\n";
        exit(0);
    } else {
        echo "\nSome tests failed. Please check the migration.\n";
        exit(1);
    }
}
?>
