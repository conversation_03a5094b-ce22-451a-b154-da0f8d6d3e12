<?php
session_start();
require_once '../config/database.php';
require_once '../models/Alumni.php';
require_once '../models/Nilai.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

if (!isset($_GET['id'])) {
    header('Location: index.php');
    exit();
}

$alumni_id = $_GET['id'];
$alumniModel = new Alumni();
$nilaiModel = new Nilai();

// Get alumni data
$alumni_data = $alumniModel->getAlumniWithGrades($alumni_id);
if (empty($alumni_data)) {
    header('Location: index.php?error=Alumni tidak ditemukan');
    exit();
}

// Get basic alumni info (first record contains basic info)
$alumni_info = $alumni_data[0];

// Get class history
$class_history = $alumniModel->getAlumniClassHistory($alumni_id);

// Group grades by academic year and semester
$grouped_grades = [];
foreach ($alumni_data as $record) {
    if ($record['nilai_id']) { // Only process records with grades
        $key = $record['tahun_ajaran'] . '_' . $record['semester'];
        if (!isset($grouped_grades[$key])) {
            $grouped_grades[$key] = [
                'tahun_ajaran' => $record['tahun_ajaran'],
                'semester' => $record['semester'],
                'kelas' => $record['historical_kelas_name'] ?? 'N/A',
                'grades' => []
            ];
        }
        $grouped_grades[$key]['grades'][] = $record;
    }
}

// Sort by academic year and semester (newest first)
uksort($grouped_grades, function($a, $b) {
    $a_parts = explode('_', $a);
    $b_parts = explode('_', $b);
    
    // Compare academic year first
    $year_compare = strcmp($b_parts[0], $a_parts[0]);
    if ($year_compare !== 0) {
        return $year_compare;
    }
    
    // Then compare semester (2 before 1)
    return $b_parts[1] - $a_parts[1];
});
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Riwayat Nilai Alumni - SIHADIR</title>
    <?php include '../template/header.php'; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <!-- Alumni Information Card -->
                <div class="card shadow mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-user-graduate me-2"></i>Riwayat Nilai Alumni
                        </h5>
                        <a href="index.php" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Kembali
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>NIS</strong></td>
                                        <td>: <?php echo htmlspecialchars($alumni_info['nis']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Nama</strong></td>
                                        <td>: <?php echo htmlspecialchars($alumni_info['nama_siswa']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Jenis Kelamin</strong></td>
                                        <td>: <?php echo $alumni_info['jenis_kelamin'] === 'L' ? 'Laki-laki' : 'Perempuan'; ?></td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td width="150"><strong>Tahun Lulus</strong></td>
                                        <td>: <?php echo htmlspecialchars($alumni_info['tahun_lulus']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>Kelas Terakhir</strong></td>
                                        <td>: <?php echo htmlspecialchars($alumni_info['kelas_terakhir']); ?></td>
                                    </tr>
                                    <tr>
                                        <td><strong>No. Telp</strong></td>
                                        <td>: <?php echo htmlspecialchars($alumni_info['no_telp'] ?? '-'); ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Class History Card -->
                <?php if (!empty($class_history)): ?>
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-school me-2"></i>Riwayat Kelas
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-sm">
                                <thead>
                                    <tr>
                                        <th>Tahun Ajaran</th>
                                        <th>Nama Kelas</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($class_history as $class): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($class['tahun_ajaran']); ?></td>
                                            <td><?php echo htmlspecialchars($class['nama_kelas']); ?></td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                $status_text = ucfirst($class['status']);
                                                switch($class['status']) {
                                                    case 'aktif':
                                                        $status_class = 'primary';
                                                        break;
                                                    case 'lulus':
                                                        $status_class = 'success';
                                                        break;
                                                    case 'pindah':
                                                        $status_class = 'warning';
                                                        break;
                                                }
                                                ?>
                                                <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Academic Grades Card -->
                <?php if (!empty($grouped_grades)): ?>
                <div class="card shadow mb-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-chart-line me-2"></i>Riwayat Nilai Akademik
                        </h6>
                    </div>
                    <div class="card-body">
                        <?php foreach ($grouped_grades as $period): ?>
                            <div class="mb-4">
                                <h6 class="text-primary border-bottom pb-2">
                                    <i class="fas fa-calendar me-2"></i>
                                    Semester <?php echo $period['semester']; ?> - 
                                    Tahun Ajaran <?php echo $period['tahun_ajaran']; ?>
                                    <small class="text-muted ms-2">(Kelas: <?php echo $period['kelas']; ?>)</small>
                                </h6>
                                
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover table-sm">
                                        <thead class="table-dark">
                                            <tr>
                                                <th>No</th>
                                                <th>Mata Pelajaran</th>
                                                <th>Tugas</th>
                                                <th>UTS</th>
                                                <th>UAS</th>
                                                <th>Absensi</th>
                                                <th>Nilai Akhir</th>
                                                <th>Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php 
                                            $no = 1;
                                            foreach ($period['grades'] as $grade): 
                                                $nilai_akhir = $grade['nilai_akhir'] ?? 0;
                                                $kkm = 75; // Default KKM
                                                $status = $nilai_akhir >= $kkm ? 'Tuntas' : 'Belum Tuntas';
                                                $status_class = $nilai_akhir >= $kkm ? 'success' : 'danger';
                                            ?>
                                                <tr>
                                                    <td><?php echo $no++; ?></td>
                                                    <td><?php echo htmlspecialchars($grade['nama_mapel']); ?></td>
                                                    <td><?php echo $grade['nilai_tugas'] ? number_format($grade['nilai_tugas'], 2) : '-'; ?></td>
                                                    <td><?php echo $grade['nilai_uts'] ? number_format($grade['nilai_uts'], 2) : '-'; ?></td>
                                                    <td><?php echo $grade['nilai_uas'] ? number_format($grade['nilai_uas'], 2) : '-'; ?></td>
                                                    <td><?php echo $grade['nilai_absen'] ? number_format($grade['nilai_absen'], 2) : '-'; ?></td>
                                                    <td>
                                                        <strong><?php echo $nilai_akhir ? number_format($nilai_akhir, 2) : '-'; ?></strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status; ?></span>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php else: ?>
                <div class="card shadow mb-4">
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            Tidak ada data nilai ditemukan untuk alumni ini.
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <?php include '../template/footer.php'; ?>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTables for better table handling
            $('.table-striped').each(function() {
                if ($(this).find('tbody tr').length > 0) {
                    $(this).DataTable({
                        "pageLength": 25,
                        "ordering": true,
                        "searching": false,
                        "lengthChange": false,
                        "info": false,
                        "paging": false
                    });
                }
            });
        });
    </script>
</body>
</html>
