# Admin Panel - Database Migration

Halaman admin untuk menjalankan database migration dengan satu klik.

## Fitur

### 🔧 Database Migration
- **One-Click Migration**: Jalankan semua migration dengan satu klik
- **Status Monitoring**: Lihat status setiap komponen migration
- **Real-time Log**: Monitor progress migration secara real-time
- **Safety Checks**: Validasi sebelum menjalankan migration

### 📊 Migration Status Dashboard
- ✅ **Siswa Status Column**: Kolom status untuk tracking siswa
- ✅ **Siswa Alumni ID Column**: Referensi ke tabel alumni
- ✅ **Alumni Siswa ID Column**: Referensi balik ke tabel siswa
- ✅ **Siswa Kelas ID Nullable**: Mengizinkan NULL untuk proses lulus
- ✅ **Siswa Kelas Constraint Fixed**: Constraint dengan SET NULL
- ✅ **Riwayat Kelas Table**: Tabel untuk tracking riwayat kelas
- ✅ **Views Created**: Views untuk academic records

## Cara Menggunakan

### 1. A<PERSON><PERSON> Migration
- Login sebagai admin
- Buka menu **Sistem** → **Database Migration**
- Atau akses langsung: `/absen/admin/migration.php`

### 2. Cek Status Migration
- Lihat status setiap komponen di panel kanan
- **Complete**: Semua migration sudah dijalankan
- **Needs Migration**: Ada komponen yang perlu di-migrate

### 3. Jalankan Migration
- Klik tombol **"Run Migration"**
- Konfirmasi dengan klik **OK** pada dialog
- Tunggu proses selesai dan lihat log hasil

### 4. Test Migration
- Klik tombol **"Test Migration"** untuk verifikasi
- Lihat hasil test untuk memastikan semua komponen berfungsi
- Pastikan semua test menunjukkan status ✓ (success)

### 5. Verifikasi Hasil
- Cek status berubah menjadi **Complete**
- Test proses lulus siswa dan naik kelas
- Pastikan tidak ada error foreign key constraint

## Yang Diperbaiki Migration Ini

### 🚫 **Masalah Sebelumnya:**
```
SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: 
a foreign key constraint fails (`db_absensi`.`siswa`, CONSTRAINT `siswa_ibfk_1` 
FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE CASCADE)
```

### ✅ **Solusi Migration:**
1. **Mengubah kolom `kelas_id`** dari NOT NULL ke NULL
2. **Mengupdate constraint** dari CASCADE ke SET NULL
3. **Menambah kolom status** untuk tracking siswa
4. **Membuat relasi bidirectional** antara siswa dan alumni
5. **Menambah tabel riwayat_kelas** untuk tracking history
6. **Membuat views** untuk academic records
7. **Menambah indexes** untuk performa

## Struktur Database Setelah Migration

### Tabel `siswa` (Modified)
```sql
- id (PK)
- nis (UNIQUE)
- nama_siswa
- jenis_kelamin
- kelas_id (NULL) ← CHANGED: Now allows NULL
- status (NEW) ← ENUM('aktif','alumni','pindah')
- alumni_id (NEW) ← References alumni.id
- alamat
- no_telp
- created_at
- updated_at
```

### Tabel `alumni` (Modified)
```sql
- id (PK)
- siswa_id (NEW) ← References siswa.id
- nis (UNIQUE)
- nama_siswa
- jenis_kelamin
- alamat
- no_telp
- tahun_lulus
- kelas_terakhir
- created_at
- updated_at
```

### Tabel `riwayat_kelas` (NEW)
```sql
- id (PK)
- siswa_id (FK → siswa.id)
- kelas_id (FK → kelas.id)
- tahun_ajaran
- status ENUM('aktif','lulus','pindah')
- created_at
- updated_at
```

### Views (NEW)
- `v_student_academic_record`: Complete student academic history
- `v_alumni_academic_record`: Complete alumni academic history

## Keamanan

### ⚠️ **Penting Sebelum Migration:**
1. **Backup Database** - Selalu backup sebelum migration
2. **Maintenance Mode** - Pastikan tidak ada user aktif
3. **Test Environment** - Test di copy database dulu jika memungkinkan

### 🔒 **Akses Control:**
- Hanya admin yang bisa mengakses halaman ini
- Session validation untuk setiap request
- Confirmation dialog sebelum menjalankan migration

## Troubleshooting

### Migration Gagal
1. **Cek Permission Database**: Pastikan user DB punya privilege ALTER
2. **Cek Koneksi Database**: Pastikan koneksi stabil
3. **Cek Log Error**: Lihat detail error di migration log
4. **Restore Backup**: Jika perlu, restore dari backup

### Constraint Error Masih Muncul
1. **Verifikasi Status**: Cek semua status menunjukkan Complete
2. **Manual Check**: Jalankan query verifikasi di dokumentasi
3. **Re-run Migration**: Coba jalankan migration lagi

### Performance Issues
1. **Check Indexes**: Pastikan indexes sudah dibuat
2. **Database Optimization**: Jalankan OPTIMIZE TABLE jika perlu
3. **Monitor Queries**: Check slow query log

## File Terkait

- `admin/migration.php` - Halaman utama migration
- `database/migrations/grade_history_preservation.sql` - SQL migration
- `database/migrations/migrate_grade_history.php` - PHP migration script
- `database/update_structure.php` - Script update db_structure_clean.sql
- `docs/GRADE_HISTORY_PRESERVATION.md` - Dokumentasi lengkap

## Versi

- **v1.0**: Initial migration system
- **v1.1**: Added constraint fixing
- **v1.2**: One-click admin interface
- **v1.3**: Updated db_structure_clean.sql

---

**Catatan**: Selalu backup database sebelum menjalankan migration!
