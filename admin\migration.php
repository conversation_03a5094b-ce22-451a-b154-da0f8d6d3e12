<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../login.php");
    exit();
}

$database = new Database();
$conn = $database->getConnection();

// Migration status check
function checkMigrationStatus($conn) {
    $status = [
        'siswa_status_column' => false,
        'siswa_alumni_id_column' => false,
        'alumni_siswa_id_column' => false,
        'siswa_kelas_id_nullable' => false,
        'siswa_kelas_constraint_fixed' => false,
        'riwayat_kelas_table' => false,
        'views_created' => false
    ];
    
    try {
        // Check siswa table columns
        $stmt = $conn->query("DESCRIBE siswa");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'status') {
                $status['siswa_status_column'] = true;
            }
            if ($column['Field'] === 'alumni_id') {
                $status['siswa_alumni_id_column'] = true;
            }
            if ($column['Field'] === 'kelas_id' && $column['Null'] === 'YES') {
                $status['siswa_kelas_id_nullable'] = true;
            }
        }
        
        // Check alumni table columns
        $stmt = $conn->query("DESCRIBE alumni");
        $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columns as $column) {
            if ($column['Field'] === 'siswa_id') {
                $status['alumni_siswa_id_column'] = true;
            }
        }
        
        // Check constraint
        $stmt = $conn->query("
            SELECT rc.DELETE_RULE
            FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
            LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
                ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
                AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
            WHERE kcu.TABLE_SCHEMA = DATABASE() 
            AND kcu.TABLE_NAME = 'siswa' 
            AND kcu.COLUMN_NAME = 'kelas_id'
            AND kcu.REFERENCED_TABLE_NAME = 'kelas'
        ");
        $constraint = $stmt->fetchColumn();
        if ($constraint === 'SET NULL') {
            $status['siswa_kelas_constraint_fixed'] = true;
        }
        
        // Check riwayat_kelas table
        $stmt = $conn->query("SHOW TABLES LIKE 'riwayat_kelas'");
        if ($stmt->rowCount() > 0) {
            $status['riwayat_kelas_table'] = true;
        }
        
        // Check views
        $stmt = $conn->query("SHOW TABLES LIKE 'v_student_academic_record'");
        if ($stmt->rowCount() > 0) {
            $status['views_created'] = true;
        }
        
    } catch (Exception $e) {
        // Ignore errors for missing tables/columns
    }
    
    return $status;
}

$migrationStatus = checkMigrationStatus($conn);
$allMigrated = !in_array(false, $migrationStatus);

// Handle migration execution
if (isset($_POST['run_migration'])) {
    $migrationLog = [];
    $transactionStarted = false;

    try {
        $conn->beginTransaction();
        $transactionStarted = true;
        
        // Step 1: Add siswa_id to alumni table
        if (!$migrationStatus['alumni_siswa_id_column']) {
            try {
                $conn->exec("ALTER TABLE `alumni` ADD COLUMN `siswa_id` INT(11) NULL AFTER `id`");
                $migrationLog[] = "✓ Added siswa_id column to alumni table";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $migrationLog[] = "Note: siswa_id column already exists in alumni table";
                } else {
                    throw $e;
                }
            }
        }
        
        // Step 2: Fix kelas_id constraint
        if (!$migrationStatus['siswa_kelas_constraint_fixed']) {
            try {
                // Drop existing constraint
                $stmt = $conn->query("
                    SELECT CONSTRAINT_NAME
                    FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
                    WHERE TABLE_SCHEMA = DATABASE()
                    AND TABLE_NAME = 'siswa'
                    AND COLUMN_NAME = 'kelas_id'
                    AND REFERENCED_TABLE_NAME = 'kelas'
                ");
                $constraintName = $stmt->fetchColumn();

                if ($constraintName) {
                    $conn->exec("ALTER TABLE `siswa` DROP FOREIGN KEY `$constraintName`");
                    $migrationLog[] = "✓ Dropped old kelas_id constraint: $constraintName";
                }
            } catch (Exception $e) {
                if (strpos($e->getMessage(), "Can't DROP") !== false) {
                    $migrationLog[] = "Note: Old constraint may not exist or already dropped";
                } else {
                    throw $e;
                }
            }
        }
        
        // Step 3: Modify kelas_id to allow NULL
        if (!$migrationStatus['siswa_kelas_id_nullable']) {
            try {
                $conn->exec("ALTER TABLE `siswa` MODIFY COLUMN `kelas_id` INT(11) NULL");
                $migrationLog[] = "✓ Modified kelas_id column to allow NULL";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'already') !== false) {
                    $migrationLog[] = "Note: kelas_id column may already allow NULL";
                } else {
                    throw $e;
                }
            }
        }
        
        // Step 4: Add status column to siswa
        if (!$migrationStatus['siswa_status_column']) {
            try {
                $conn->exec("ALTER TABLE `siswa` ADD COLUMN `status` ENUM('aktif', 'alumni', 'pindah') NOT NULL DEFAULT 'aktif' AFTER `kelas_id`");
                $migrationLog[] = "✓ Added status column to siswa table";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $migrationLog[] = "Note: status column already exists in siswa table";
                } else {
                    throw $e;
                }
            }
        }
        
        // Step 5: Add alumni_id to siswa
        if (!$migrationStatus['siswa_alumni_id_column']) {
            try {
                $conn->exec("ALTER TABLE `siswa` ADD COLUMN `alumni_id` INT(11) NULL AFTER `status`");
                $migrationLog[] = "✓ Added alumni_id column to siswa table";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    $migrationLog[] = "Note: alumni_id column already exists in siswa table";
                } else {
                    throw $e;
                }
            }
        }
        
        // Step 6: Add new kelas_id constraint with SET NULL
        if (!$migrationStatus['siswa_kelas_constraint_fixed']) {
            try {
                $conn->exec("ALTER TABLE `siswa` ADD CONSTRAINT `siswa_kelas_fk` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE SET NULL ON UPDATE CASCADE");
                $migrationLog[] = "✓ Added new kelas_id constraint with SET NULL";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                    $migrationLog[] = "Note: kelas_id constraint may already exist";
                } else {
                    throw $e;
                }
            }
        }
        
        // Step 7: Create riwayat_kelas table
        if (!$migrationStatus['riwayat_kelas_table']) {
            try {
                $conn->exec("
                    CREATE TABLE `riwayat_kelas` (
                        `id` int(11) NOT NULL AUTO_INCREMENT,
                        `siswa_id` int(11) NOT NULL,
                        `kelas_id` int(11) NOT NULL,
                        `tahun_ajaran` varchar(20) NOT NULL,
                        `status` enum('aktif','lulus','pindah') NOT NULL DEFAULT 'aktif',
                        `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                        `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                        PRIMARY KEY (`id`),
                        KEY `siswa_id` (`siswa_id`),
                        KEY `kelas_id` (`kelas_id`)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci
                ");
                $migrationLog[] = "✓ Created riwayat_kelas table";

                // Add foreign keys separately to handle potential errors
                try {
                    $conn->exec("ALTER TABLE `riwayat_kelas` ADD CONSTRAINT `riwayat_kelas_ibfk_1` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE");
                    $migrationLog[] = "✓ Added riwayat_kelas siswa_id foreign key";
                } catch (Exception $e) {
                    if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                        $migrationLog[] = "Warning: Could not add siswa_id foreign key: " . $e->getMessage();
                    }
                }

                try {
                    $conn->exec("ALTER TABLE `riwayat_kelas` ADD CONSTRAINT `riwayat_kelas_ibfk_2` FOREIGN KEY (`kelas_id`) REFERENCES `kelas` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE");
                    $migrationLog[] = "✓ Added riwayat_kelas kelas_id foreign key";
                } catch (Exception $e) {
                    if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                        $migrationLog[] = "Warning: Could not add kelas_id foreign key: " . $e->getMessage();
                    }
                }

            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'already exists') !== false) {
                    $migrationLog[] = "Note: riwayat_kelas table already exists";
                } else {
                    throw $e;
                }
            }
        }
        
        // Step 8: Add foreign key constraints
        try {
            $conn->exec("ALTER TABLE `alumni` ADD CONSTRAINT `alumni_siswa_fk` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE SET NULL ON UPDATE CASCADE");
            $migrationLog[] = "✓ Added alumni-siswa foreign key";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                throw $e;
            }
        }

        try {
            $conn->exec("ALTER TABLE `siswa` ADD CONSTRAINT `siswa_alumni_fk` FOREIGN KEY (`alumni_id`) REFERENCES `alumni` (`id`) ON DELETE SET NULL ON UPDATE CASCADE");
            $migrationLog[] = "✓ Added siswa-alumni foreign key";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                throw $e;
            }
        }

        // Step 9: Create views for academic records
        if (!$migrationStatus['views_created']) {
            // Create student academic record view
            $conn->exec("
                CREATE OR REPLACE VIEW `v_student_academic_record` AS
                SELECT
                    s.id as siswa_id,
                    s.nis,
                    s.nama_siswa,
                    s.jenis_kelamin,
                    s.status as student_status,
                    s.alamat,
                    s.no_telp,
                    k.nama_kelas as current_kelas,
                    a.tahun_lulus,
                    a.kelas_terakhir,
                    n.id as nilai_id,
                    n.mapel_id,
                    mp.nama_mapel,
                    n.nilai_tugas,
                    n.nilai_uts,
                    n.nilai_uas,
                    n.nilai_absen,
                    n.nilai_akhir,
                    n.semester,
                    n.tahun_ajaran,
                    rk.kelas_id as historical_kelas_id,
                    k_hist.nama_kelas as historical_kelas_name,
                    rk.status as class_status
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                LEFT JOIN alumni a ON s.alumni_id = a.id
                LEFT JOIN nilai n ON s.id = n.siswa_id
                LEFT JOIN mata_pelajaran mp ON n.mapel_id = mp.id
                LEFT JOIN riwayat_kelas rk ON s.id = rk.siswa_id
                LEFT JOIN kelas k_hist ON rk.kelas_id = k_hist.id
            ");
            $migrationLog[] = "✓ Created student academic record view";

            // Create alumni academic record view
            $conn->exec("
                CREATE OR REPLACE VIEW `v_alumni_academic_record` AS
                SELECT
                    a.id as alumni_id,
                    a.nis,
                    a.nama_siswa,
                    a.jenis_kelamin,
                    a.alamat,
                    a.no_telp,
                    a.tahun_lulus,
                    a.kelas_terakhir,
                    s.id as siswa_id,
                    n.id as nilai_id,
                    n.mapel_id,
                    mp.nama_mapel,
                    n.nilai_tugas,
                    n.nilai_uts,
                    n.nilai_uas,
                    n.nilai_absen,
                    n.nilai_akhir,
                    n.semester,
                    n.tahun_ajaran,
                    rk.kelas_id as historical_kelas_id,
                    k_hist.nama_kelas as historical_kelas_name,
                    rk.status as class_status
                FROM alumni a
                LEFT JOIN siswa s ON a.siswa_id = s.id
                LEFT JOIN nilai n ON s.id = n.siswa_id
                LEFT JOIN mata_pelajaran mp ON n.mapel_id = mp.id
                LEFT JOIN riwayat_kelas rk ON s.id = rk.siswa_id
                LEFT JOIN kelas k_hist ON rk.kelas_id = k_hist.id
            ");
            $migrationLog[] = "✓ Created alumni academic record view";
        }

        // Step 10: Create indexes for better performance
        try {
            $conn->exec("CREATE INDEX `idx_siswa_status_nis` ON `siswa` (`status`, `nis`)");
            $migrationLog[] = "✓ Created siswa status-nis index";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                $migrationLog[] = "Note: siswa status-nis index might already exist";
            }
        }

        try {
            $conn->exec("CREATE INDEX `idx_nilai_siswa_semester_tahun` ON `nilai` (`siswa_id`, `semester`, `tahun_ajaran`)");
            $migrationLog[] = "✓ Created nilai performance index";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                $migrationLog[] = "Note: nilai performance index might already exist";
            }
        }

        try {
            $conn->exec("CREATE INDEX `idx_riwayat_kelas_siswa_tahun` ON `riwayat_kelas` (`siswa_id`, `tahun_ajaran`)");
            $migrationLog[] = "✓ Created riwayat_kelas performance index";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') === false) {
                $migrationLog[] = "Note: riwayat_kelas performance index might already exist";
            }
        }
        
        if ($transactionStarted) {
            $conn->commit();
        }
        $success = true;
        $message = "Migration completed successfully!";

    } catch (Exception $e) {
        if ($transactionStarted) {
            try {
                $conn->rollback();
            } catch (Exception $rollbackError) {
                // Ignore rollback errors if transaction was already closed
                $migrationLog[] = "Note: Transaction rollback failed (transaction may have been auto-closed)";
            }
        }
        $success = false;
        $message = "Migration failed: " . $e->getMessage();
        $migrationLog[] = "✗ Error: " . $e->getMessage();
    }
}

include '../template/header.php';
?>

    <!-- Main Content -->
    <div id="content">
        <div class="container-fluid">
            <!-- Top Bar -->
            <div class="topbar">
                <button class="btn btn-link d-md-none" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="topbar-divider d-none d-sm-block"></div>
                <div class="topbar-user">
                    <span class="mr-2 d-none d-lg-inline text-gray-600 small">
                        <?php echo htmlspecialchars($_SESSION['nama_lengkap']); ?>
                    </span>
                    <i class="fas fa-user-circle fa-fw"></i>
                </div>
            </div>

            <main class="main-content">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Database Migration</h1>
                </div>

            <?php if (isset($success)): ?>
                <div class="alert alert-<?= $success ? 'success' : 'danger' ?> alert-dismissible fade show">
                    <?= htmlspecialchars($message) ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                
                <?php if (!empty($migrationLog)): ?>
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5>Migration Log</h5>
                        </div>
                        <div class="card-body">
                            <pre class="mb-0"><?= implode("\n", $migrationLog) ?></pre>
                        </div>
                    </div>
                <?php endif; ?>
            <?php endif; ?>

            <div class="row">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h5>Grade History Preservation Migration</h5>
                        </div>
                        <div class="card-body">
                            <p>This migration will update your database to support grade history preservation and fix graduation process issues.</p>
                            
                            <h6>What this migration does:</h6>
                            <ul>
                                <li>Adds status tracking for students (aktif, alumni, pindah)</li>
                                <li>Creates bidirectional relationship between students and alumni</li>
                                <li>Fixes foreign key constraints to allow graduation process</li>
                                <li>Creates class history tracking table</li>
                                <li>Preserves all student grade data when graduating</li>
                            </ul>
                            
                            <h6>Critical Fix Included:</h6>
                            <div class="alert alert-warning">
                                <strong>Fixes graduation error:</strong><br>
                                <code>SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails</code>
                            </div>
                            
                            <?php if ($allMigrated): ?>
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i> All migrations have been applied successfully!
                                </div>
                                <div class="mt-3">
                                    <a href="test_migration.php" class="btn btn-info">
                                        <i class="fas fa-vial"></i> Test Migration
                                    </a>
                                </div>
                            <?php else: ?>
                                <form method="POST" onsubmit="return confirm('Are you sure you want to run the migration? Make sure you have backed up your database first!')">
                                    <button type="submit" name="run_migration" class="btn btn-primary">
                                        <i class="fas fa-play"></i> Run Migration
                                    </button>
                                </form>
                                <div class="mt-3">
                                    <a href="test_migration.php" class="btn btn-outline-info">
                                        <i class="fas fa-vial"></i> Test Current State
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h5>Migration Status</h5>
                        </div>
                        <div class="card-body">
                            <?php foreach ($migrationStatus as $key => $status): ?>
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="small"><?= ucwords(str_replace('_', ' ', $key)) ?></span>
                                    <?php if ($status): ?>
                                        <span class="badge bg-success">✓</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Pending</span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                            
                            <hr>
                            <div class="d-flex justify-content-between align-items-center">
                                <strong>Overall Status:</strong>
                                <?php if ($allMigrated): ?>
                                    <span class="badge bg-success">Complete</span>
                                <?php else: ?>
                                    <span class="badge bg-warning">Needs Migration</span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card mt-3">
                        <div class="card-header">
                            <h5>⚠️ Important</h5>
                        </div>
                        <div class="card-body">
                            <p class="small mb-2"><strong>Before running migration:</strong></p>
                            <ul class="small">
                                <li>Backup your database</li>
                                <li>Ensure no users are actively using the system</li>
                                <li>Test on a copy first if possible</li>
                            </ul>
                            
                            <p class="small mb-0"><strong>After migration:</strong></p>
                            <ul class="small mb-0">
                                <li>Test student graduation process</li>
                                <li>Test student promotion process</li>
                                <li>Verify grade history is preserved</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            </main>
        </div>
    </div>

<?php include '../template/footer.php'; ?>
