<?php
require_once __DIR__ . '/../config/database.php';

class Alumni {
    private $db;
    private $conn;

    public function __construct() {
        $this->db = new Database();
        $this->conn = $this->db->getConnection();
    }

    public function addAlumni($data) {
        $query = "INSERT INTO alumni (nis, nama_siswa, jenis_kelamin, alamat, no_telp, tahun_lulus, kelas_terakhir)
                  VALUES (:nis, :nama_siswa, :jenis_kelamin, :alamat, :no_telp, :tahun_lulus, :kelas_terakhir)";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([
                ':nis' => $data['nis'],
                ':nama_siswa' => $data['nama_siswa'],
                ':jenis_kelamin' => $data['jenis_kelamin'],
                ':alamat' => $data['alamat'],
                ':no_telp' => $data['no_telp'],
                ':tahun_lulus' => $data['tahun_lulus'],
                ':kelas_terakhir' => $data['kelas_terakhir']
            ]);
            return true;
        } catch(PDOException $e) {
            return false;
        }
    }

    public function addAlumniWithHistory($data) {
        $query = "INSERT INTO alumni (nis, nama_siswa, jenis_kelamin, alamat, no_telp, tahun_lulus, kelas_terakhir, siswa_id)
                  VALUES (:nis, :nama_siswa, :jenis_kelamin, :alamat, :no_telp, :tahun_lulus, :kelas_terakhir, :siswa_id)";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([
                ':nis' => $data['nis'],
                ':nama_siswa' => $data['nama_siswa'],
                ':jenis_kelamin' => $data['jenis_kelamin'],
                ':alamat' => $data['alamat'],
                ':no_telp' => $data['no_telp'],
                ':tahun_lulus' => $data['tahun_lulus'],
                ':kelas_terakhir' => $data['kelas_terakhir'],
                ':siswa_id' => $data['siswa_id']
            ]);
            return $this->conn->lastInsertId();
        } catch(PDOException $e) {
            return false;
        }
    }

    public function getAlumniByNIS($nis) {
        $query = "SELECT * FROM alumni WHERE nis = :nis";
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([':nis' => $nis]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return false;
        }
    }

    public function getAllAlumni() {
        $query = "SELECT * FROM alumni ORDER BY tahun_lulus DESC, nama_siswa ASC";
        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return [];
        }
    }

    public function getAlumniWithGrades($alumni_id) {
        $query = "SELECT
                    a.*,
                    s.id as siswa_id,
                    n.id as nilai_id,
                    n.mapel_id,
                    mp.nama_mapel,
                    n.nilai_tugas,
                    n.nilai_uts,
                    n.nilai_uas,
                    n.nilai_absen,
                    n.nilai_akhir,
                    n.semester,
                    n.tahun_ajaran,
                    rk.kelas_id as historical_kelas_id,
                    k.nama_kelas as historical_kelas_name,
                    rk.status as class_status
                  FROM alumni a
                  LEFT JOIN siswa s ON a.siswa_id = s.id
                  LEFT JOIN nilai n ON s.id = n.siswa_id
                  LEFT JOIN mata_pelajaran mp ON n.mapel_id = mp.id
                  LEFT JOIN riwayat_kelas rk ON s.id = rk.siswa_id
                  LEFT JOIN kelas k ON rk.kelas_id = k.id
                  WHERE a.id = :alumni_id
                  ORDER BY n.tahun_ajaran DESC, n.semester DESC, mp.nama_mapel ASC";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([':alumni_id' => $alumni_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return [];
        }
    }

    public function getAlumniGradesByNIS($nis) {
        $query = "SELECT
                    a.*,
                    s.id as siswa_id,
                    n.id as nilai_id,
                    n.mapel_id,
                    mp.nama_mapel,
                    n.nilai_tugas,
                    n.nilai_uts,
                    n.nilai_uas,
                    n.nilai_absen,
                    n.nilai_akhir,
                    n.semester,
                    n.tahun_ajaran,
                    rk.kelas_id as historical_kelas_id,
                    k.nama_kelas as historical_kelas_name,
                    rk.status as class_status
                  FROM alumni a
                  LEFT JOIN siswa s ON a.siswa_id = s.id
                  LEFT JOIN nilai n ON s.id = n.siswa_id
                  LEFT JOIN mata_pelajaran mp ON n.mapel_id = mp.id
                  LEFT JOIN riwayat_kelas rk ON s.id = rk.siswa_id
                  LEFT JOIN kelas k ON rk.kelas_id = k.id
                  WHERE a.nis = :nis
                  ORDER BY n.tahun_ajaran DESC, n.semester DESC, mp.nama_mapel ASC";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([':nis' => $nis]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return [];
        }
    }

    public function getAlumniClassHistory($alumni_id) {
        $query = "SELECT
                    rk.*,
                    k.nama_kelas,
                    k.tahun_ajaran as kelas_tahun_ajaran
                  FROM alumni a
                  JOIN siswa s ON a.siswa_id = s.id
                  JOIN riwayat_kelas rk ON s.id = rk.siswa_id
                  JOIN kelas k ON rk.kelas_id = k.id
                  WHERE a.id = :alumni_id
                  ORDER BY rk.tahun_ajaran ASC";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([':alumni_id' => $alumni_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return [];
        }
    }

    public function getAlumniAttendanceHistory($alumni_id) {
        $query = "SELECT
                    da.*,
                    ab.tanggal,
                    k.nama_kelas,
                    mp.nama_mapel
                  FROM alumni a
                  JOIN siswa s ON a.siswa_id = s.id
                  JOIN detail_absensi da ON s.id = da.siswa_id
                  JOIN absensi ab ON da.absensi_id = ab.id
                  JOIN kelas k ON ab.kelas_id = k.id
                  JOIN mata_pelajaran mp ON ab.mapel_id = mp.id
                  WHERE a.id = :alumni_id
                  ORDER BY ab.tanggal DESC";

        try {
            $stmt = $this->conn->prepare($query);
            $stmt->execute([':alumni_id' => $alumni_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch(PDOException $e) {
            return [];
        }
    }
}
