<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../login.php");
    exit();
}

$database = new Database();
$conn = $database->getConnection();

echo "<!DOCTYPE html>
<html>
<head>
    <title>Test Migration</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>";

echo "<h1>Migration Test Results</h1>";

try {
    // Test 1: Check siswa table structure
    echo "<h2>1. Testing Siswa Table Structure</h2>";
    $stmt = $conn->query("DESCRIBE siswa");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasStatus = false;
    $hasAlumniId = false;
    $kelasIdNullable = false;
    
    foreach ($columns as $column) {
        if ($column['Field'] === 'status') $hasStatus = true;
        if ($column['Field'] === 'alumni_id') $hasAlumniId = true;
        if ($column['Field'] === 'kelas_id' && $column['Null'] === 'YES') $kelasIdNullable = true;
    }
    
    echo $hasStatus ? "<p class='success'>✓ Status column exists</p>" : "<p class='error'>✗ Status column missing</p>";
    echo $hasAlumniId ? "<p class='success'>✓ Alumni_id column exists</p>" : "<p class='error'>✗ Alumni_id column missing</p>";
    echo $kelasIdNullable ? "<p class='success'>✓ Kelas_id allows NULL</p>" : "<p class='error'>✗ Kelas_id does not allow NULL</p>";
    
    // Test 2: Check alumni table structure
    echo "<h2>2. Testing Alumni Table Structure</h2>";
    $stmt = $conn->query("DESCRIBE alumni");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    $hasSiswaId = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'siswa_id') $hasSiswaId = true;
    }
    
    echo $hasSiswaId ? "<p class='success'>✓ Siswa_id column exists in alumni</p>" : "<p class='error'>✗ Siswa_id column missing in alumni</p>";
    
    // Test 3: Check riwayat_kelas table
    echo "<h2>3. Testing Riwayat_kelas Table</h2>";
    $stmt = $conn->query("SHOW TABLES LIKE 'riwayat_kelas'");
    $tableExists = $stmt->rowCount() > 0;
    
    echo $tableExists ? "<p class='success'>✓ Riwayat_kelas table exists</p>" : "<p class='error'>✗ Riwayat_kelas table missing</p>";
    
    // Test 4: Check foreign key constraints
    echo "<h2>4. Testing Foreign Key Constraints</h2>";
    $stmt = $conn->query("
        SELECT 
            kcu.CONSTRAINT_NAME,
            kcu.COLUMN_NAME,
            kcu.REFERENCED_TABLE_NAME,
            rc.DELETE_RULE,
            rc.UPDATE_RULE
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu
        LEFT JOIN INFORMATION_SCHEMA.REFERENTIAL_CONSTRAINTS rc 
            ON kcu.CONSTRAINT_NAME = rc.CONSTRAINT_NAME 
            AND kcu.TABLE_SCHEMA = rc.CONSTRAINT_SCHEMA
        WHERE kcu.TABLE_SCHEMA = DATABASE() 
        AND kcu.TABLE_NAME = 'siswa' 
        AND kcu.REFERENCED_TABLE_NAME IS NOT NULL
    ");
    
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $kelasConstraintFixed = false;
    $alumniConstraintExists = false;
    
    foreach ($constraints as $constraint) {
        if ($constraint['COLUMN_NAME'] === 'kelas_id' && $constraint['DELETE_RULE'] === 'SET NULL') {
            $kelasConstraintFixed = true;
        }
        if ($constraint['COLUMN_NAME'] === 'alumni_id') {
            $alumniConstraintExists = true;
        }
    }
    
    echo $kelasConstraintFixed ? "<p class='success'>✓ Kelas_id constraint uses SET NULL</p>" : "<p class='error'>✗ Kelas_id constraint not fixed</p>";
    echo $alumniConstraintExists ? "<p class='success'>✓ Alumni_id constraint exists</p>" : "<p class='error'>✗ Alumni_id constraint missing</p>";
    
    // Test 5: Check views
    echo "<h2>5. Testing Database Views</h2>";
    $stmt = $conn->query("SHOW TABLES LIKE 'v_student_academic_record'");
    $studentViewExists = $stmt->rowCount() > 0;
    
    $stmt = $conn->query("SHOW TABLES LIKE 'v_alumni_academic_record'");
    $alumniViewExists = $stmt->rowCount() > 0;
    
    echo $studentViewExists ? "<p class='success'>✓ Student academic record view exists</p>" : "<p class='error'>✗ Student academic record view missing</p>";
    echo $alumniViewExists ? "<p class='success'>✓ Alumni academic record view exists</p>" : "<p class='error'>✗ Alumni academic record view missing</p>";
    
    // Test 6: Test graduation query
    echo "<h2>6. Testing Graduation Query</h2>";
    try {
        $stmt = $conn->prepare("SELECT id FROM siswa WHERE status = 'aktif' LIMIT 1");
        $stmt->execute();
        $testStudent = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testStudent) {
            // Test the problematic query (prepare only, don't execute)
            $testQuery = "UPDATE siswa SET status = 'alumni', alumni_id = NULL, kelas_id = NULL WHERE id = :siswa_id";
            $stmt = $conn->prepare($testQuery);
            echo "<p class='success'>✓ Graduation query can be prepared successfully</p>";
            echo "<p class='success'>✓ Setting kelas_id = NULL should now work without foreign key errors</p>";
        } else {
            echo "<p class='warning'>⚠ No active students found for testing</p>";
        }
    } catch (Exception $e) {
        echo "<p class='error'>✗ Graduation query test failed: " . $e->getMessage() . "</p>";
    }
    
    // Test 7: Show current constraint details
    echo "<h2>7. Current Constraint Details</h2>";
    echo "<pre>";
    foreach ($constraints as $constraint) {
        echo sprintf("%-20s %-15s %-20s %-15s %-15s\n",
            $constraint['CONSTRAINT_NAME'],
            $constraint['COLUMN_NAME'],
            $constraint['REFERENCED_TABLE_NAME'],
            $constraint['DELETE_RULE'],
            $constraint['UPDATE_RULE']
        );
    }
    echo "</pre>";
    
    // Summary
    echo "<h2>Summary</h2>";
    $allTests = [
        $hasStatus && $hasAlumniId && $kelasIdNullable,
        $hasSiswaId,
        $tableExists,
        $kelasConstraintFixed && $alumniConstraintExists,
        $studentViewExists && $alumniViewExists
    ];
    
    $passedTests = count(array_filter($allTests));
    $totalTests = count($allTests);
    
    if ($passedTests === $totalTests) {
        echo "<p class='success'><strong>✓ All tests passed! Migration completed successfully.</strong></p>";
        echo "<p>You can now safely use the graduation and promotion features.</p>";
    } else {
        echo "<p class='error'><strong>✗ Some tests failed. Migration may be incomplete.</strong></p>";
        echo "<p>Passed: $passedTests/$totalTests tests</p>";
        echo "<p>Please run the migration again or check the error messages above.</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='error'>Error running tests: " . $e->getMessage() . "</p>";
}

echo "<p><a href='migration.php'>← Back to Migration</a></p>";
echo "</body></html>";
?>
